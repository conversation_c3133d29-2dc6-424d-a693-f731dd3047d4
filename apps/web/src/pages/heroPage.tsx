import React, { useState } from "react";
import { useSession, signIn } from "next-auth/react";
import { useRouter } from "next/navigation";
import { useDispatch, UseDispatch } from "react-redux";
import { setCommit } from "../../../../packages/slice/repoSlice";
const Heropage = () => {
  const [repoUrl, setRepoUrl] = useState("");
  const { data: session, status } = useSession();
  const router = useRouter();
  const dispatch = useDispatch();
  const handleExplore = async () => {
    const response = await fetch("http://localhost:3001/api/repo", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ repoUrl }),
    });
    const data = await response.json();
    console.log(data);{
    "name": "Keshav.dev",
    "description": null,
    "language": {
        "TypeScript": 204979,
        "CSS": 6644,
        "JavaScript": 819
    },
    "commits": [
        {
            "sha": "629c8ec22a935cf44ef73e0cafba9853d48eb511",
            "node_id": "C_kwDOOeh35toAKDYyOWM4ZWMyMmE5MzVjZjQ0ZWY3M2UwY2FmYmE5ODUzZDQ4ZWI1MTE",
            "commit": {
                "author": {
                    "name": "Keshav raj",
                    "email": "<EMAIL>",
                    "date": "2025-07-06T11:15:13Z"
                },
                "committer": {
                    "name": "Keshav raj",
                    "email": "<EMAIL>",
                    "date": "2025-07-06T11:15:13Z"
                },
                "message": "fix global.css",
                "tree": {
                    "sha": "a909fd560c3d770d69d415aecefc7fcaefc63316",
                    "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/git/trees/a909fd560c3d770d69d415aecefc7fcaefc63316"
                },
                "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/git/commits/629c8ec22a935cf44ef73e0cafba9853d48eb511",
                "comment_count": 0,
                "verification": {
                    "verified": false,
                    "reason": "unsigned",
                    "signature": null,
                    "payload": null,
                    "verified_at": null
                }
            },
            "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/commits/629c8ec22a935cf44ef73e0cafba9853d48eb511",
            "html_url": "https://github.com/itz-rajkeshav/Keshav.dev/commit/629c8ec22a935cf44ef73e0cafba9853d48eb511",
            "comments_url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/commits/629c8ec22a935cf44ef73e0cafba9853d48eb511/comments",
            "author": {
                "login": "itz-rajkeshav",
                "id": 150330725,
                "node_id": "U_kgDOCPXdZQ",
                "avatar_url": "https://avatars.githubusercontent.com/u/150330725?v=4",
                "gravatar_id": "",
                "url": "https://api.github.com/users/itz-rajkeshav",
                "html_url": "https://github.com/itz-rajkeshav",
                "followers_url": "https://api.github.com/users/itz-rajkeshav/followers",
                "following_url": "https://api.github.com/users/itz-rajkeshav/following{/other_user}",
                "gists_url": "https://api.github.com/users/itz-rajkeshav/gists{/gist_id}",
                "starred_url": "https://api.github.com/users/itz-rajkeshav/starred{/owner}{/repo}",
                "subscriptions_url": "https://api.github.com/users/itz-rajkeshav/subscriptions",
                "organizations_url": "https://api.github.com/users/itz-rajkeshav/orgs",
                "repos_url": "https://api.github.com/users/itz-rajkeshav/repos",
                "events_url": "https://api.github.com/users/itz-rajkeshav/events{/privacy}",
                "received_events_url": "https://api.github.com/users/itz-rajkeshav/received_events",
                "type": "User",
                "user_view_type": "public",
                "site_admin": false
            },
            "committer": {
                "login": "itz-rajkeshav",
                "id": 150330725,
                "node_id": "U_kgDOCPXdZQ",
                "avatar_url": "https://avatars.githubusercontent.com/u/150330725?v=4",
                "gravatar_id": "",
                "url": "https://api.github.com/users/itz-rajkeshav",
                "html_url": "https://github.com/itz-rajkeshav",
                "followers_url": "https://api.github.com/users/itz-rajkeshav/followers",
                "following_url": "https://api.github.com/users/itz-rajkeshav/following{/other_user}",
                "gists_url": "https://api.github.com/users/itz-rajkeshav/gists{/gist_id}",
                "starred_url": "https://api.github.com/users/itz-rajkeshav/starred{/owner}{/repo}",
                "subscriptions_url": "https://api.github.com/users/itz-rajkeshav/subscriptions",
                "organizations_url": "https://api.github.com/users/itz-rajkeshav/orgs",
                "repos_url": "https://api.github.com/users/itz-rajkeshav/repos",
                "events_url": "https://api.github.com/users/itz-rajkeshav/events{/privacy}",
                "received_events_url": "https://api.github.com/users/itz-rajkeshav/received_events",
                "type": "User",
                "user_view_type": "public",
                "site_admin": false
            },
            "parents": [
                {
                    "sha": "11153274e417e18b8bd8452c4d8ebe131023bb8c",
                    "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/commits/11153274e417e18b8bd8452c4d8ebe131023bb8c",
                    "html_url": "https://github.com/itz-rajkeshav/Keshav.dev/commit/11153274e417e18b8bd8452c4d8ebe131023bb8c"
                }
            ]
        },
        {
            "sha": "11153274e417e18b8bd8452c4d8ebe131023bb8c",
            "node_id": "C_kwDOOeh35toAKDExMTUzMjc0ZTQxN2UxOGI4YmQ4NDUyYzRkOGViZTEzMTAyM2JiOGM",
            "commit": {
                "author": {
                    "name": "Keshav raj",
                    "email": "<EMAIL>",
                    "date": "2025-07-06T06:50:22Z"
                },
                "committer": {
                    "name": "Keshav raj",
                    "email": "<EMAIL>",
                    "date": "2025-07-06T06:50:22Z"
                },
                "message": "add analytics",
                "tree": {
                    "sha": "a4f18b7a1df25431debc1524aa018911f9dec2e3",
                    "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/git/trees/a4f18b7a1df25431debc1524aa018911f9dec2e3"
                },
                "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/git/commits/11153274e417e18b8bd8452c4d8ebe131023bb8c",
                "comment_count": 0,
                "verification": {
                    "verified": false,
                    "reason": "unsigned",
                    "signature": null,
                    "payload": null,
                    "verified_at": null
                }
            },
            "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/commits/11153274e417e18b8bd8452c4d8ebe131023bb8c",
            "html_url": "https://github.com/itz-rajkeshav/Keshav.dev/commit/11153274e417e18b8bd8452c4d8ebe131023bb8c",
            "comments_url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/commits/11153274e417e18b8bd8452c4d8ebe131023bb8c/comments",
            "author": {
                "login": "itz-rajkeshav",
                "id": 150330725,
                "node_id": "U_kgDOCPXdZQ",
                "avatar_url": "https://avatars.githubusercontent.com/u/150330725?v=4",
                "gravatar_id": "",
                "url": "https://api.github.com/users/itz-rajkeshav",
                "html_url": "https://github.com/itz-rajkeshav",
                "followers_url": "https://api.github.com/users/itz-rajkeshav/followers",
                "following_url": "https://api.github.com/users/itz-rajkeshav/following{/other_user}",
                "gists_url": "https://api.github.com/users/itz-rajkeshav/gists{/gist_id}",
                "starred_url": "https://api.github.com/users/itz-rajkeshav/starred{/owner}{/repo}",
                "subscriptions_url": "https://api.github.com/users/itz-rajkeshav/subscriptions",
                "organizations_url": "https://api.github.com/users/itz-rajkeshav/orgs",
                "repos_url": "https://api.github.com/users/itz-rajkeshav/repos",
                "events_url": "https://api.github.com/users/itz-rajkeshav/events{/privacy}",
                "received_events_url": "https://api.github.com/users/itz-rajkeshav/received_events",
                "type": "User",
                "user_view_type": "public",
                "site_admin": false
            },
            "committer": {
                "login": "itz-rajkeshav",
                "id": 150330725,
                "node_id": "U_kgDOCPXdZQ",
                "avatar_url": "https://avatars.githubusercontent.com/u/150330725?v=4",
                "gravatar_id": "",
                "url": "https://api.github.com/users/itz-rajkeshav",
                "html_url": "https://github.com/itz-rajkeshav",
                "followers_url": "https://api.github.com/users/itz-rajkeshav/followers",
                "following_url": "https://api.github.com/users/itz-rajkeshav/following{/other_user}",
                "gists_url": "https://api.github.com/users/itz-rajkeshav/gists{/gist_id}",
                "starred_url": "https://api.github.com/users/itz-rajkeshav/starred{/owner}{/repo}",
                "subscriptions_url": "https://api.github.com/users/itz-rajkeshav/subscriptions",
                "organizations_url": "https://api.github.com/users/itz-rajkeshav/orgs",
                "repos_url": "https://api.github.com/users/itz-rajkeshav/repos",
                "events_url": "https://api.github.com/users/itz-rajkeshav/events{/privacy}",
                "received_events_url": "https://api.github.com/users/itz-rajkeshav/received_events",
                "type": "User",
                "user_view_type": "public",
                "site_admin": false
            },
            "parents": [
                {
                    "sha": "21fc97a1d44846b8bbb31e2a8140ea003830de4c",
                    "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/commits/21fc97a1d44846b8bbb31e2a8140ea003830de4c",
                    "html_url": "https://github.com/itz-rajkeshav/Keshav.dev/commit/21fc97a1d44846b8bbb31e2a8140ea003830de4c"
                }
            ]
        },
        {
            "sha": "21fc97a1d44846b8bbb31e2a8140ea003830de4c",
            "node_id": "C_kwDOOeh35toAKDIxZmM5N2ExZDQ0ODQ2YjhiYmIzMWUyYTgxNDBlYTAwMzgzMGRlNGM",
            "commit": {
                "author": {
                    "name": "Keshav raj",
                    "email": "<EMAIL>",
                    "date": "2025-07-06T06:36:06Z"
                },
                "committer": {
                    "name": "Keshav raj",
                    "email": "<EMAIL>",
                    "date": "2025-07-06T06:36:06Z"
                },
                "message": "fix memory leak",
                "tree": {
                    "sha": "b51ac307cd79660e6a1453cb93e37e418baf072b",
                    "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/git/trees/b51ac307cd79660e6a1453cb93e37e418baf072b"
                },
                "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/git/commits/21fc97a1d44846b8bbb31e2a8140ea003830de4c",
                "comment_count": 0,
                "verification": {
                    "verified": false,
                    "reason": "unsigned",
                    "signature": null,
                    "payload": null,
                    "verified_at": null
                }
            },
            "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/commits/21fc97a1d44846b8bbb31e2a8140ea003830de4c",
            "html_url": "https://github.com/itz-rajkeshav/Keshav.dev/commit/21fc97a1d44846b8bbb31e2a8140ea003830de4c",
            "comments_url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/commits/21fc97a1d44846b8bbb31e2a8140ea003830de4c/comments",
            "author": {
                "login": "itz-rajkeshav",
                "id": 150330725,
                "node_id": "U_kgDOCPXdZQ",
                "avatar_url": "https://avatars.githubusercontent.com/u/150330725?v=4",
                "gravatar_id": "",
                "url": "https://api.github.com/users/itz-rajkeshav",
                "html_url": "https://github.com/itz-rajkeshav",
                "followers_url": "https://api.github.com/users/itz-rajkeshav/followers",
                "following_url": "https://api.github.com/users/itz-rajkeshav/following{/other_user}",
                "gists_url": "https://api.github.com/users/itz-rajkeshav/gists{/gist_id}",
                "starred_url": "https://api.github.com/users/itz-rajkeshav/starred{/owner}{/repo}",
                "subscriptions_url": "https://api.github.com/users/itz-rajkeshav/subscriptions",
                "organizations_url": "https://api.github.com/users/itz-rajkeshav/orgs",
                "repos_url": "https://api.github.com/users/itz-rajkeshav/repos",
                "events_url": "https://api.github.com/users/itz-rajkeshav/events{/privacy}",
                "received_events_url": "https://api.github.com/users/itz-rajkeshav/received_events",
                "type": "User",
                "user_view_type": "public",
                "site_admin": false
            },
            "committer": {
                "login": "itz-rajkeshav",
                "id": 150330725,
                "node_id": "U_kgDOCPXdZQ",
                "avatar_url": "https://avatars.githubusercontent.com/u/150330725?v=4",
                "gravatar_id": "",
                "url": "https://api.github.com/users/itz-rajkeshav",
                "html_url": "https://github.com/itz-rajkeshav",
                "followers_url": "https://api.github.com/users/itz-rajkeshav/followers",
                "following_url": "https://api.github.com/users/itz-rajkeshav/following{/other_user}",
                "gists_url": "https://api.github.com/users/itz-rajkeshav/gists{/gist_id}",
                "starred_url": "https://api.github.com/users/itz-rajkeshav/starred{/owner}{/repo}",
                "subscriptions_url": "https://api.github.com/users/itz-rajkeshav/subscriptions",
                "organizations_url": "https://api.github.com/users/itz-rajkeshav/orgs",
                "repos_url": "https://api.github.com/users/itz-rajkeshav/repos",
                "events_url": "https://api.github.com/users/itz-rajkeshav/events{/privacy}",
                "received_events_url": "https://api.github.com/users/itz-rajkeshav/received_events",
                "type": "User",
                "user_view_type": "public",
                "site_admin": false
            },
            "parents": [
                {
                    "sha": "772041b6cb7c9ee29478638cd4a2245d5e3e7838",
                    "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/commits/772041b6cb7c9ee29478638cd4a2245d5e3e7838",
                    "html_url": "https://github.com/itz-rajkeshav/Keshav.dev/commit/772041b6cb7c9ee29478638cd4a2245d5e3e7838"
                }
            ]
        },
        {
            "sha": "772041b6cb7c9ee29478638cd4a2245d5e3e7838",
            "node_id": "C_kwDOOeh35toAKDc3MjA0MWI2Y2I3YzllZTI5NDc4NjM4Y2Q0YTIyNDVkNWUzZTc4Mzg",
            "commit": {
                "author": {
                    "name": "Keshav raj",
                    "email": "<EMAIL>",
                    "date": "2025-06-27T10:48:12Z"
                },
                "committer": {
                    "name": "Keshav raj",
                    "email": "<EMAIL>",
                    "date": "2025-06-27T10:48:12Z"
                },
                "message": "implement little animation",
                "tree": {
                    "sha": "1428540f6ef17443c899fa70544ca3866bb010cb",
                    "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/git/trees/1428540f6ef17443c899fa70544ca3866bb010cb"
                },
                "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/git/commits/772041b6cb7c9ee29478638cd4a2245d5e3e7838",
                "comment_count": 0,
                "verification": {
                    "verified": false,
                    "reason": "unsigned",
                    "signature": null,
                    "payload": null,
                    "verified_at": null
                }
            },
            "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/commits/772041b6cb7c9ee29478638cd4a2245d5e3e7838",
            "html_url": "https://github.com/itz-rajkeshav/Keshav.dev/commit/772041b6cb7c9ee29478638cd4a2245d5e3e7838",
            "comments_url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/commits/772041b6cb7c9ee29478638cd4a2245d5e3e7838/comments",
            "author": {
                "login": "itz-rajkeshav",
                "id": 150330725,
                "node_id": "U_kgDOCPXdZQ",
                "avatar_url": "https://avatars.githubusercontent.com/u/150330725?v=4",
                "gravatar_id": "",
                "url": "https://api.github.com/users/itz-rajkeshav",
                "html_url": "https://github.com/itz-rajkeshav",
                "followers_url": "https://api.github.com/users/itz-rajkeshav/followers",
                "following_url": "https://api.github.com/users/itz-rajkeshav/following{/other_user}",
                "gists_url": "https://api.github.com/users/itz-rajkeshav/gists{/gist_id}",
                "starred_url": "https://api.github.com/users/itz-rajkeshav/starred{/owner}{/repo}",
                "subscriptions_url": "https://api.github.com/users/itz-rajkeshav/subscriptions",
                "organizations_url": "https://api.github.com/users/itz-rajkeshav/orgs",
                "repos_url": "https://api.github.com/users/itz-rajkeshav/repos",
                "events_url": "https://api.github.com/users/itz-rajkeshav/events{/privacy}",
                "received_events_url": "https://api.github.com/users/itz-rajkeshav/received_events",
                "type": "User",
                "user_view_type": "public",
                "site_admin": false
            },
            "committer": {
                "login": "itz-rajkeshav",
                "id": 150330725,
                "node_id": "U_kgDOCPXdZQ",
                "avatar_url": "https://avatars.githubusercontent.com/u/150330725?v=4",
                "gravatar_id": "",
                "url": "https://api.github.com/users/itz-rajkeshav",
                "html_url": "https://github.com/itz-rajkeshav",
                "followers_url": "https://api.github.com/users/itz-rajkeshav/followers",
                "following_url": "https://api.github.com/users/itz-rajkeshav/following{/other_user}",
                "gists_url": "https://api.github.com/users/itz-rajkeshav/gists{/gist_id}",
                "starred_url": "https://api.github.com/users/itz-rajkeshav/starred{/owner}{/repo}",
                "subscriptions_url": "https://api.github.com/users/itz-rajkeshav/subscriptions",
                "organizations_url": "https://api.github.com/users/itz-rajkeshav/orgs",
                "repos_url": "https://api.github.com/users/itz-rajkeshav/repos",
                "events_url": "https://api.github.com/users/itz-rajkeshav/events{/privacy}",
                "received_events_url": "https://api.github.com/users/itz-rajkeshav/received_events",
                "type": "User",
                "user_view_type": "public",
                "site_admin": false
            },
            "parents": [
                {
                    "sha": "639945e444f95bfa1ad3d4661d6bf35fc5fddf9b",
                    "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/commits/639945e444f95bfa1ad3d4661d6bf35fc5fddf9b",
                    "html_url": "https://github.com/itz-rajkeshav/Keshav.dev/commit/639945e444f95bfa1ad3d4661d6bf35fc5fddf9b"
                }
            ]
        },
        {
            "sha": "639945e444f95bfa1ad3d4661d6bf35fc5fddf9b",
            "node_id": "C_kwDOOeh35toAKDYzOTk0NWU0NDRmOTViZmExYWQzZDQ2NjFkNmJmMzVmYzVmZGRmOWI",
            "commit": {
                "author": {
                    "name": "Keshav raj",
                    "email": "<EMAIL>",
                    "date": "2025-05-02T18:53:25Z"
                },
                "committer": {
                    "name": "Keshav raj",
                    "email": "<EMAIL>",
                    "date": "2025-05-02T18:53:25Z"
                },
                "message": "fix: update copyright year",
                "tree": {
                    "sha": "9e7bc58409a4492896039f4385819a13cc08bf55",
                    "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/git/trees/9e7bc58409a4492896039f4385819a13cc08bf55"
                },
                "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/git/commits/639945e444f95bfa1ad3d4661d6bf35fc5fddf9b",
                "comment_count": 0,
                "verification": {
                    "verified": false,
                    "reason": "unsigned",
                    "signature": null,
                    "payload": null,
                    "verified_at": null
                }
            },
            "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/commits/639945e444f95bfa1ad3d4661d6bf35fc5fddf9b",
            "html_url": "https://github.com/itz-rajkeshav/Keshav.dev/commit/639945e444f95bfa1ad3d4661d6bf35fc5fddf9b",
            "comments_url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/commits/639945e444f95bfa1ad3d4661d6bf35fc5fddf9b/comments",
            "author": {
                "login": "itz-rajkeshav",
                "id": 150330725,
                "node_id": "U_kgDOCPXdZQ",
                "avatar_url": "https://avatars.githubusercontent.com/u/150330725?v=4",
                "gravatar_id": "",
                "url": "https://api.github.com/users/itz-rajkeshav",
                "html_url": "https://github.com/itz-rajkeshav",
                "followers_url": "https://api.github.com/users/itz-rajkeshav/followers",
                "following_url": "https://api.github.com/users/itz-rajkeshav/following{/other_user}",
                "gists_url": "https://api.github.com/users/itz-rajkeshav/gists{/gist_id}",
                "starred_url": "https://api.github.com/users/itz-rajkeshav/starred{/owner}{/repo}",
                "subscriptions_url": "https://api.github.com/users/itz-rajkeshav/subscriptions",
                "organizations_url": "https://api.github.com/users/itz-rajkeshav/orgs",
                "repos_url": "https://api.github.com/users/itz-rajkeshav/repos",
                "events_url": "https://api.github.com/users/itz-rajkeshav/events{/privacy}",
                "received_events_url": "https://api.github.com/users/itz-rajkeshav/received_events",
                "type": "User",
                "user_view_type": "public",
                "site_admin": false
            },
            "committer": {
                "login": "itz-rajkeshav",
                "id": 150330725,
                "node_id": "U_kgDOCPXdZQ",
                "avatar_url": "https://avatars.githubusercontent.com/u/150330725?v=4",
                "gravatar_id": "",
                "url": "https://api.github.com/users/itz-rajkeshav",
                "html_url": "https://github.com/itz-rajkeshav",
                "followers_url": "https://api.github.com/users/itz-rajkeshav/followers",
                "following_url": "https://api.github.com/users/itz-rajkeshav/following{/other_user}",
                "gists_url": "https://api.github.com/users/itz-rajkeshav/gists{/gist_id}",
                "starred_url": "https://api.github.com/users/itz-rajkeshav/starred{/owner}{/repo}",
                "subscriptions_url": "https://api.github.com/users/itz-rajkeshav/subscriptions",
                "organizations_url": "https://api.github.com/users/itz-rajkeshav/orgs",
                "repos_url": "https://api.github.com/users/itz-rajkeshav/repos",
                "events_url": "https://api.github.com/users/itz-rajkeshav/events{/privacy}",
                "received_events_url": "https://api.github.com/users/itz-rajkeshav/received_events",
                "type": "User",
                "user_view_type": "public",
                "site_admin": false
            },
            "parents": [
                {
                    "sha": "639c0e4e795225683a91b582ab26934b77dfd6e6",
                    "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/commits/639c0e4e795225683a91b582ab26934b77dfd6e6",
                    "html_url": "https://github.com/itz-rajkeshav/Keshav.dev/commit/639c0e4e795225683a91b582ab26934b77dfd6e6"
                }
            ]
        },
        {
            "sha": "639c0e4e795225683a91b582ab26934b77dfd6e6",
            "node_id": "C_kwDOOeh35toAKDYzOWMwZTRlNzk1MjI1NjgzYTkxYjU4MmFiMjY5MzRiNzdkZmQ2ZTY",
            "commit": {
                "author": {
                    "name": "Keshav raj",
                    "email": "<EMAIL>",
                    "date": "2025-05-02T18:34:11Z"
                },
                "committer": {
                    "name": "Keshav raj",
                    "email": "<EMAIL>",
                    "date": "2025-05-02T18:34:11Z"
                },
                "message": "add the icon and fix download cv.",
                "tree": {
                    "sha": "ac61fe1a4394bc672d68f1d6f37448a8838158ae",
                    "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/git/trees/ac61fe1a4394bc672d68f1d6f37448a8838158ae"
                },
                "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/git/commits/639c0e4e795225683a91b582ab26934b77dfd6e6",
                "comment_count": 0,
                "verification": {
                    "verified": false,
                    "reason": "unsigned",
                    "signature": null,
                    "payload": null,
                    "verified_at": null
                }
            },
            "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/commits/639c0e4e795225683a91b582ab26934b77dfd6e6",
            "html_url": "https://github.com/itz-rajkeshav/Keshav.dev/commit/639c0e4e795225683a91b582ab26934b77dfd6e6",
            "comments_url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/commits/639c0e4e795225683a91b582ab26934b77dfd6e6/comments",
            "author": {
                "login": "itz-rajkeshav",
                "id": 150330725,
                "node_id": "U_kgDOCPXdZQ",
                "avatar_url": "https://avatars.githubusercontent.com/u/150330725?v=4",
                "gravatar_id": "",
                "url": "https://api.github.com/users/itz-rajkeshav",
                "html_url": "https://github.com/itz-rajkeshav",
                "followers_url": "https://api.github.com/users/itz-rajkeshav/followers",
                "following_url": "https://api.github.com/users/itz-rajkeshav/following{/other_user}",
                "gists_url": "https://api.github.com/users/itz-rajkeshav/gists{/gist_id}",
                "starred_url": "https://api.github.com/users/itz-rajkeshav/starred{/owner}{/repo}",
                "subscriptions_url": "https://api.github.com/users/itz-rajkeshav/subscriptions",
                "organizations_url": "https://api.github.com/users/itz-rajkeshav/orgs",
                "repos_url": "https://api.github.com/users/itz-rajkeshav/repos",
                "events_url": "https://api.github.com/users/itz-rajkeshav/events{/privacy}",
                "received_events_url": "https://api.github.com/users/itz-rajkeshav/received_events",
                "type": "User",
                "user_view_type": "public",
                "site_admin": false
            },
            "committer": {
                "login": "itz-rajkeshav",
                "id": 150330725,
                "node_id": "U_kgDOCPXdZQ",
                "avatar_url": "https://avatars.githubusercontent.com/u/150330725?v=4",
                "gravatar_id": "",
                "url": "https://api.github.com/users/itz-rajkeshav",
                "html_url": "https://github.com/itz-rajkeshav",
                "followers_url": "https://api.github.com/users/itz-rajkeshav/followers",
                "following_url": "https://api.github.com/users/itz-rajkeshav/following{/other_user}",
                "gists_url": "https://api.github.com/users/itz-rajkeshav/gists{/gist_id}",
                "starred_url": "https://api.github.com/users/itz-rajkeshav/starred{/owner}{/repo}",
                "subscriptions_url": "https://api.github.com/users/itz-rajkeshav/subscriptions",
                "organizations_url": "https://api.github.com/users/itz-rajkeshav/orgs",
                "repos_url": "https://api.github.com/users/itz-rajkeshav/repos",
                "events_url": "https://api.github.com/users/itz-rajkeshav/events{/privacy}",
                "received_events_url": "https://api.github.com/users/itz-rajkeshav/received_events",
                "type": "User",
                "user_view_type": "public",
                "site_admin": false
            },
            "parents": [
                {
                    "sha": "9eca34046134e22c67f731c90d314bec12dac8d4",
                    "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/commits/9eca34046134e22c67f731c90d314bec12dac8d4",
                    "html_url": "https://github.com/itz-rajkeshav/Keshav.dev/commit/9eca34046134e22c67f731c90d314bec12dac8d4"
                }
            ]
        },
        {
            "sha": "9eca34046134e22c67f731c90d314bec12dac8d4",
            "node_id": "C_kwDOOeh35toAKDllY2EzNDA0NjEzNGUyMmM2N2Y3MzFjOTBkMzE0YmVjMTJkYWM4ZDQ",
            "commit": {
                "author": {
                    "name": "Keshav raj",
                    "email": "<EMAIL>",
                    "date": "2025-05-02T12:59:20Z"
                },
                "committer": {
                    "name": "Keshav raj",
                    "email": "<EMAIL>",
                    "date": "2025-05-02T12:59:20Z"
                },
                "message": "check",
                "tree": {
                    "sha": "e9798bd27267b7937c7be2c3a58817528e7084ec",
                    "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/git/trees/e9798bd27267b7937c7be2c3a58817528e7084ec"
                },
                "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/git/commits/9eca34046134e22c67f731c90d314bec12dac8d4",
                "comment_count": 0,
                "verification": {
                    "verified": false,
                    "reason": "unsigned",
                    "signature": null,
                    "payload": null,
                    "verified_at": null
                }
            },
            "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/commits/9eca34046134e22c67f731c90d314bec12dac8d4",
            "html_url": "https://github.com/itz-rajkeshav/Keshav.dev/commit/9eca34046134e22c67f731c90d314bec12dac8d4",
            "comments_url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/commits/9eca34046134e22c67f731c90d314bec12dac8d4/comments",
            "author": {
                "login": "itz-rajkeshav",
                "id": 150330725,
                "node_id": "U_kgDOCPXdZQ",
                "avatar_url": "https://avatars.githubusercontent.com/u/150330725?v=4",
                "gravatar_id": "",
                "url": "https://api.github.com/users/itz-rajkeshav",
                "html_url": "https://github.com/itz-rajkeshav",
                "followers_url": "https://api.github.com/users/itz-rajkeshav/followers",
                "following_url": "https://api.github.com/users/itz-rajkeshav/following{/other_user}",
                "gists_url": "https://api.github.com/users/itz-rajkeshav/gists{/gist_id}",
                "starred_url": "https://api.github.com/users/itz-rajkeshav/starred{/owner}{/repo}",
                "subscriptions_url": "https://api.github.com/users/itz-rajkeshav/subscriptions",
                "organizations_url": "https://api.github.com/users/itz-rajkeshav/orgs",
                "repos_url": "https://api.github.com/users/itz-rajkeshav/repos",
                "events_url": "https://api.github.com/users/itz-rajkeshav/events{/privacy}",
                "received_events_url": "https://api.github.com/users/itz-rajkeshav/received_events",
                "type": "User",
                "user_view_type": "public",
                "site_admin": false
            },
            "committer": {
                "login": "itz-rajkeshav",
                "id": 150330725,
                "node_id": "U_kgDOCPXdZQ",
                "avatar_url": "https://avatars.githubusercontent.com/u/150330725?v=4",
                "gravatar_id": "",
                "url": "https://api.github.com/users/itz-rajkeshav",
                "html_url": "https://github.com/itz-rajkeshav",
                "followers_url": "https://api.github.com/users/itz-rajkeshav/followers",
                "following_url": "https://api.github.com/users/itz-rajkeshav/following{/other_user}",
                "gists_url": "https://api.github.com/users/itz-rajkeshav/gists{/gist_id}",
                "starred_url": "https://api.github.com/users/itz-rajkeshav/starred{/owner}{/repo}",
                "subscriptions_url": "https://api.github.com/users/itz-rajkeshav/subscriptions",
                "organizations_url": "https://api.github.com/users/itz-rajkeshav/orgs",
                "repos_url": "https://api.github.com/users/itz-rajkeshav/repos",
                "events_url": "https://api.github.com/users/itz-rajkeshav/events{/privacy}",
                "received_events_url": "https://api.github.com/users/itz-rajkeshav/received_events",
                "type": "User",
                "user_view_type": "public",
                "site_admin": false
            },
            "parents": [
                {
                    "sha": "dee956add9fcf013a08b46a2477e984440516c17",
                    "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/commits/dee956add9fcf013a08b46a2477e984440516c17",
                    "html_url": "https://github.com/itz-rajkeshav/Keshav.dev/commit/dee956add9fcf013a08b46a2477e984440516c17"
                }
            ]
        },
        {
            "sha": "dee956add9fcf013a08b46a2477e984440516c17",
            "node_id": "C_kwDOOeh35toAKGRlZTk1NmFkZDlmY2YwMTNhMDhiNDZhMjQ3N2U5ODQ0NDA1MTZjMTc",
            "commit": {
                "author": {
                    "name": "Keshav raj",
                    "email": "<EMAIL>",
                    "date": "2025-05-02T07:21:22Z"
                },
                "committer": {
                    "name": "Keshav raj",
                    "email": "<EMAIL>",
                    "date": "2025-05-02T07:21:22Z"
                },
                "message": "refactor: update color palette",
                "tree": {
                    "sha": "6bb5b6ed27225bc4e37426c5ddff6db3ad449fac",
                    "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/git/trees/6bb5b6ed27225bc4e37426c5ddff6db3ad449fac"
                },
                "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/git/commits/dee956add9fcf013a08b46a2477e984440516c17",
                "comment_count": 0,
                "verification": {
                    "verified": false,
                    "reason": "unsigned",
                    "signature": null,
                    "payload": null,
                    "verified_at": null
                }
            },
            "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/commits/dee956add9fcf013a08b46a2477e984440516c17",
            "html_url": "https://github.com/itz-rajkeshav/Keshav.dev/commit/dee956add9fcf013a08b46a2477e984440516c17",
            "comments_url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/commits/dee956add9fcf013a08b46a2477e984440516c17/comments",
            "author": {
                "login": "itz-rajkeshav",
                "id": 150330725,
                "node_id": "U_kgDOCPXdZQ",
                "avatar_url": "https://avatars.githubusercontent.com/u/150330725?v=4",
                "gravatar_id": "",
                "url": "https://api.github.com/users/itz-rajkeshav",
                "html_url": "https://github.com/itz-rajkeshav",
                "followers_url": "https://api.github.com/users/itz-rajkeshav/followers",
                "following_url": "https://api.github.com/users/itz-rajkeshav/following{/other_user}",
                "gists_url": "https://api.github.com/users/itz-rajkeshav/gists{/gist_id}",
                "starred_url": "https://api.github.com/users/itz-rajkeshav/starred{/owner}{/repo}",
                "subscriptions_url": "https://api.github.com/users/itz-rajkeshav/subscriptions",
                "organizations_url": "https://api.github.com/users/itz-rajkeshav/orgs",
                "repos_url": "https://api.github.com/users/itz-rajkeshav/repos",
                "events_url": "https://api.github.com/users/itz-rajkeshav/events{/privacy}",
                "received_events_url": "https://api.github.com/users/itz-rajkeshav/received_events",
                "type": "User",
                "user_view_type": "public",
                "site_admin": false
            },
            "committer": {
                "login": "itz-rajkeshav",
                "id": 150330725,
                "node_id": "U_kgDOCPXdZQ",
                "avatar_url": "https://avatars.githubusercontent.com/u/150330725?v=4",
                "gravatar_id": "",
                "url": "https://api.github.com/users/itz-rajkeshav",
                "html_url": "https://github.com/itz-rajkeshav",
                "followers_url": "https://api.github.com/users/itz-rajkeshav/followers",
                "following_url": "https://api.github.com/users/itz-rajkeshav/following{/other_user}",
                "gists_url": "https://api.github.com/users/itz-rajkeshav/gists{/gist_id}",
                "starred_url": "https://api.github.com/users/itz-rajkeshav/starred{/owner}{/repo}",
                "subscriptions_url": "https://api.github.com/users/itz-rajkeshav/subscriptions",
                "organizations_url": "https://api.github.com/users/itz-rajkeshav/orgs",
                "repos_url": "https://api.github.com/users/itz-rajkeshav/repos",
                "events_url": "https://api.github.com/users/itz-rajkeshav/events{/privacy}",
                "received_events_url": "https://api.github.com/users/itz-rajkeshav/received_events",
                "type": "User",
                "user_view_type": "public",
                "site_admin": false
            },
            "parents": [
                {
                    "sha": "6cc83841000a3502e57ccef03b5998a5da780c62",
                    "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/commits/6cc83841000a3502e57ccef03b5998a5da780c62",
                    "html_url": "https://github.com/itz-rajkeshav/Keshav.dev/commit/6cc83841000a3502e57ccef03b5998a5da780c62"
                }
            ]
        },
        {
            "sha": "6cc83841000a3502e57ccef03b5998a5da780c62",
            "node_id": "C_kwDOOeh35toAKDZjYzgzODQxMDAwYTM1MDJlNTdjY2VmMDNiNTk5OGE1ZGE3ODBjNjI",
            "commit": {
                "author": {
                    "name": "Keshav raj",
                    "email": "<EMAIL>",
                    "date": "2025-05-01T10:37:52Z"
                },
                "committer": {
                    "name": "Keshav raj",
                    "email": "<EMAIL>",
                    "date": "2025-05-01T10:37:52Z"
                },
                "message": "fix: adjust input field styling for better mobile responsive",
                "tree": {
                    "sha": "55734ebc63d092b65010f1e589132a16a51ae805",
                    "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/git/trees/55734ebc63d092b65010f1e589132a16a51ae805"
                },
                "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/git/commits/6cc83841000a3502e57ccef03b5998a5da780c62",
                "comment_count": 0,
                "verification": {
                    "verified": false,
                    "reason": "unsigned",
                    "signature": null,
                    "payload": null,
                    "verified_at": null
                }
            },
            "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/commits/6cc83841000a3502e57ccef03b5998a5da780c62",
            "html_url": "https://github.com/itz-rajkeshav/Keshav.dev/commit/6cc83841000a3502e57ccef03b5998a5da780c62",
            "comments_url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/commits/6cc83841000a3502e57ccef03b5998a5da780c62/comments",
            "author": {
                "login": "itz-rajkeshav",
                "id": 150330725,
                "node_id": "U_kgDOCPXdZQ",
                "avatar_url": "https://avatars.githubusercontent.com/u/150330725?v=4",
                "gravatar_id": "",
                "url": "https://api.github.com/users/itz-rajkeshav",
                "html_url": "https://github.com/itz-rajkeshav",
                "followers_url": "https://api.github.com/users/itz-rajkeshav/followers",
                "following_url": "https://api.github.com/users/itz-rajkeshav/following{/other_user}",
                "gists_url": "https://api.github.com/users/itz-rajkeshav/gists{/gist_id}",
                "starred_url": "https://api.github.com/users/itz-rajkeshav/starred{/owner}{/repo}",
                "subscriptions_url": "https://api.github.com/users/itz-rajkeshav/subscriptions",
                "organizations_url": "https://api.github.com/users/itz-rajkeshav/orgs",
                "repos_url": "https://api.github.com/users/itz-rajkeshav/repos",
                "events_url": "https://api.github.com/users/itz-rajkeshav/events{/privacy}",
                "received_events_url": "https://api.github.com/users/itz-rajkeshav/received_events",
                "type": "User",
                "user_view_type": "public",
                "site_admin": false
            },
            "committer": {
                "login": "itz-rajkeshav",
                "id": 150330725,
                "node_id": "U_kgDOCPXdZQ",
                "avatar_url": "https://avatars.githubusercontent.com/u/150330725?v=4",
                "gravatar_id": "",
                "url": "https://api.github.com/users/itz-rajkeshav",
                "html_url": "https://github.com/itz-rajkeshav",
                "followers_url": "https://api.github.com/users/itz-rajkeshav/followers",
                "following_url": "https://api.github.com/users/itz-rajkeshav/following{/other_user}",
                "gists_url": "https://api.github.com/users/itz-rajkeshav/gists{/gist_id}",
                "starred_url": "https://api.github.com/users/itz-rajkeshav/starred{/owner}{/repo}",
                "subscriptions_url": "https://api.github.com/users/itz-rajkeshav/subscriptions",
                "organizations_url": "https://api.github.com/users/itz-rajkeshav/orgs",
                "repos_url": "https://api.github.com/users/itz-rajkeshav/repos",
                "events_url": "https://api.github.com/users/itz-rajkeshav/events{/privacy}",
                "received_events_url": "https://api.github.com/users/itz-rajkeshav/received_events",
                "type": "User",
                "user_view_type": "public",
                "site_admin": false
            },
            "parents": [
                {
                    "sha": "71034649483db0ba777fa9fffb54c05be079b63c",
                    "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/commits/71034649483db0ba777fa9fffb54c05be079b63c",
                    "html_url": "https://github.com/itz-rajkeshav/Keshav.dev/commit/71034649483db0ba777fa9fffb54c05be079b63c"
                }
            ]
        },
        {
            "sha": "71034649483db0ba777fa9fffb54c05be079b63c",
            "node_id": "C_kwDOOeh35toAKDcxMDM0NjQ5NDgzZGIwYmE3NzdmYTlmZmZiNTRjMDViZTA3OWI2M2M",
            "commit": {
                "author": {
                    "name": "Keshav raj",
                    "email": "<EMAIL>",
                    "date": "2025-05-01T10:28:46Z"
                },
                "committer": {
                    "name": "Keshav raj",
                    "email": "<EMAIL>",
                    "date": "2025-05-01T10:28:46Z"
                },
                "message": "Refactor image handling and optimize component imports\n- Replaced <img> tags with <Image>\n- Added priority and sizes attributes to <Image> components for improved loading behavior.\n- Lazy-loaded components in the main page to enhance initial load performance.",
                "tree": {
                    "sha": "ca6d8f9bd57856f98ff134d8b62f4e9b52fe0f2f",
                    "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/git/trees/ca6d8f9bd57856f98ff134d8b62f4e9b52fe0f2f"
                },
                "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/git/commits/71034649483db0ba777fa9fffb54c05be079b63c",
                "comment_count": 0,
                "verification": {
                    "verified": false,
                    "reason": "unsigned",
                    "signature": null,
                    "payload": null,
                    "verified_at": null
                }
            },
            "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/commits/71034649483db0ba777fa9fffb54c05be079b63c",
            "html_url": "https://github.com/itz-rajkeshav/Keshav.dev/commit/71034649483db0ba777fa9fffb54c05be079b63c",
            "comments_url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/commits/71034649483db0ba777fa9fffb54c05be079b63c/comments",
            "author": {
                "login": "itz-rajkeshav",
                "id": 150330725,
                "node_id": "U_kgDOCPXdZQ",
                "avatar_url": "https://avatars.githubusercontent.com/u/150330725?v=4",
                "gravatar_id": "",
                "url": "https://api.github.com/users/itz-rajkeshav",
                "html_url": "https://github.com/itz-rajkeshav",
                "followers_url": "https://api.github.com/users/itz-rajkeshav/followers",
                "following_url": "https://api.github.com/users/itz-rajkeshav/following{/other_user}",
                "gists_url": "https://api.github.com/users/itz-rajkeshav/gists{/gist_id}",
                "starred_url": "https://api.github.com/users/itz-rajkeshav/starred{/owner}{/repo}",
                "subscriptions_url": "https://api.github.com/users/itz-rajkeshav/subscriptions",
                "organizations_url": "https://api.github.com/users/itz-rajkeshav/orgs",
                "repos_url": "https://api.github.com/users/itz-rajkeshav/repos",
                "events_url": "https://api.github.com/users/itz-rajkeshav/events{/privacy}",
                "received_events_url": "https://api.github.com/users/itz-rajkeshav/received_events",
                "type": "User",
                "user_view_type": "public",
                "site_admin": false
            },
            "committer": {
                "login": "itz-rajkeshav",
                "id": 150330725,
                "node_id": "U_kgDOCPXdZQ",
                "avatar_url": "https://avatars.githubusercontent.com/u/150330725?v=4",
                "gravatar_id": "",
                "url": "https://api.github.com/users/itz-rajkeshav",
                "html_url": "https://github.com/itz-rajkeshav",
                "followers_url": "https://api.github.com/users/itz-rajkeshav/followers",
                "following_url": "https://api.github.com/users/itz-rajkeshav/following{/other_user}",
                "gists_url": "https://api.github.com/users/itz-rajkeshav/gists{/gist_id}",
                "starred_url": "https://api.github.com/users/itz-rajkeshav/starred{/owner}{/repo}",
                "subscriptions_url": "https://api.github.com/users/itz-rajkeshav/subscriptions",
                "organizations_url": "https://api.github.com/users/itz-rajkeshav/orgs",
                "repos_url": "https://api.github.com/users/itz-rajkeshav/repos",
                "events_url": "https://api.github.com/users/itz-rajkeshav/events{/privacy}",
                "received_events_url": "https://api.github.com/users/itz-rajkeshav/received_events",
                "type": "User",
                "user_view_type": "public",
                "site_admin": false
            },
            "parents": [
                {
                    "sha": "abea1fa9f8ac0958b8ac3014b6a3fa1671c75ddd",
                    "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/commits/abea1fa9f8ac0958b8ac3014b6a3fa1671c75ddd",
                    "html_url": "https://github.com/itz-rajkeshav/Keshav.dev/commit/abea1fa9f8ac0958b8ac3014b6a3fa1671c75ddd"
                }
            ]
        },
        {
            "sha": "abea1fa9f8ac0958b8ac3014b6a3fa1671c75ddd",
            "node_id": "C_kwDOOeh35toAKGFiZWExZmE5ZjhhYzA5NThiOGFjMzAxNGI2YTNmYTE2NzFjNzVkZGQ",
            "commit": {
                "author": {
                    "name": "Keshav raj",
                    "email": "<EMAIL>",
                    "date": "2025-04-30T17:20:48Z"
                },
                "committer": {
                    "name": "Keshav raj",
                    "email": "<EMAIL>",
                    "date": "2025-04-30T17:20:48Z"
                },
                "message": "refactor: enhance drag-and-drop functionality with mobile support and boundary constraints",
                "tree": {
                    "sha": "69448f5d177d527cae2e7db613bf013d86148c7b",
                    "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/git/trees/69448f5d177d527cae2e7db613bf013d86148c7b"
                },
                "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/git/commits/abea1fa9f8ac0958b8ac3014b6a3fa1671c75ddd",
                "comment_count": 0,
                "verification": {
                    "verified": false,
                    "reason": "unsigned",
                    "signature": null,
                    "payload": null,
                    "verified_at": null
                }
            },
            "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/commits/abea1fa9f8ac0958b8ac3014b6a3fa1671c75ddd",
            "html_url": "https://github.com/itz-rajkeshav/Keshav.dev/commit/abea1fa9f8ac0958b8ac3014b6a3fa1671c75ddd",
            "comments_url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/commits/abea1fa9f8ac0958b8ac3014b6a3fa1671c75ddd/comments",
            "author": {
                "login": "itz-rajkeshav",
                "id": 150330725,
                "node_id": "U_kgDOCPXdZQ",
                "avatar_url": "https://avatars.githubusercontent.com/u/150330725?v=4",
                "gravatar_id": "",
                "url": "https://api.github.com/users/itz-rajkeshav",
                "html_url": "https://github.com/itz-rajkeshav",
                "followers_url": "https://api.github.com/users/itz-rajkeshav/followers",
                "following_url": "https://api.github.com/users/itz-rajkeshav/following{/other_user}",
                "gists_url": "https://api.github.com/users/itz-rajkeshav/gists{/gist_id}",
                "starred_url": "https://api.github.com/users/itz-rajkeshav/starred{/owner}{/repo}",
                "subscriptions_url": "https://api.github.com/users/itz-rajkeshav/subscriptions",
                "organizations_url": "https://api.github.com/users/itz-rajkeshav/orgs",
                "repos_url": "https://api.github.com/users/itz-rajkeshav/repos",
                "events_url": "https://api.github.com/users/itz-rajkeshav/events{/privacy}",
                "received_events_url": "https://api.github.com/users/itz-rajkeshav/received_events",
                "type": "User",
                "user_view_type": "public",
                "site_admin": false
            },
            "committer": {
                "login": "itz-rajkeshav",
                "id": 150330725,
                "node_id": "U_kgDOCPXdZQ",
                "avatar_url": "https://avatars.githubusercontent.com/u/150330725?v=4",
                "gravatar_id": "",
                "url": "https://api.github.com/users/itz-rajkeshav",
                "html_url": "https://github.com/itz-rajkeshav",
                "followers_url": "https://api.github.com/users/itz-rajkeshav/followers",
                "following_url": "https://api.github.com/users/itz-rajkeshav/following{/other_user}",
                "gists_url": "https://api.github.com/users/itz-rajkeshav/gists{/gist_id}",
                "starred_url": "https://api.github.com/users/itz-rajkeshav/starred{/owner}{/repo}",
                "subscriptions_url": "https://api.github.com/users/itz-rajkeshav/subscriptions",
                "organizations_url": "https://api.github.com/users/itz-rajkeshav/orgs",
                "repos_url": "https://api.github.com/users/itz-rajkeshav/repos",
                "events_url": "https://api.github.com/users/itz-rajkeshav/events{/privacy}",
                "received_events_url": "https://api.github.com/users/itz-rajkeshav/received_events",
                "type": "User",
                "user_view_type": "public",
                "site_admin": false
            },
            "parents": [
                {
                    "sha": "4f6c8f90d9119cfdf7e58f208afc03fdb767a28e",
                    "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/commits/4f6c8f90d9119cfdf7e58f208afc03fdb767a28e",
                    "html_url": "https://github.com/itz-rajkeshav/Keshav.dev/commit/4f6c8f90d9119cfdf7e58f208afc03fdb767a28e"
                }
            ]
        },
        {
            "sha": "4f6c8f90d9119cfdf7e58f208afc03fdb767a28e",
            "node_id": "C_kwDOOeh35toAKDRmNmM4ZjkwZDkxMTljZmRmN2U1OGYyMDhhZmMwM2ZkYjc2N2EyOGU",
            "commit": {
                "author": {
                    "name": "Keshav raj",
                    "email": "<EMAIL>",
                    "date": "2025-04-29T20:29:39Z"
                },
                "committer": {
                    "name": "Keshav raj",
                    "email": "<EMAIL>",
                    "date": "2025-04-29T20:29:39Z"
                },
                "message": "fix little thing",
                "tree": {
                    "sha": "2e1953b3173c1cc9edab5eb20b318c143d60e0d3",
                    "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/git/trees/2e1953b3173c1cc9edab5eb20b318c143d60e0d3"
                },
                "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/git/commits/4f6c8f90d9119cfdf7e58f208afc03fdb767a28e",
                "comment_count": 0,
                "verification": {
                    "verified": false,
                    "reason": "unsigned",
                    "signature": null,
                    "payload": null,
                    "verified_at": null
                }
            },
            "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/commits/4f6c8f90d9119cfdf7e58f208afc03fdb767a28e",
            "html_url": "https://github.com/itz-rajkeshav/Keshav.dev/commit/4f6c8f90d9119cfdf7e58f208afc03fdb767a28e",
            "comments_url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/commits/4f6c8f90d9119cfdf7e58f208afc03fdb767a28e/comments",
            "author": {
                "login": "itz-rajkeshav",
                "id": 150330725,
                "node_id": "U_kgDOCPXdZQ",
                "avatar_url": "https://avatars.githubusercontent.com/u/150330725?v=4",
                "gravatar_id": "",
                "url": "https://api.github.com/users/itz-rajkeshav",
                "html_url": "https://github.com/itz-rajkeshav",
                "followers_url": "https://api.github.com/users/itz-rajkeshav/followers",
                "following_url": "https://api.github.com/users/itz-rajkeshav/following{/other_user}",
                "gists_url": "https://api.github.com/users/itz-rajkeshav/gists{/gist_id}",
                "starred_url": "https://api.github.com/users/itz-rajkeshav/starred{/owner}{/repo}",
                "subscriptions_url": "https://api.github.com/users/itz-rajkeshav/subscriptions",
                "organizations_url": "https://api.github.com/users/itz-rajkeshav/orgs",
                "repos_url": "https://api.github.com/users/itz-rajkeshav/repos",
                "events_url": "https://api.github.com/users/itz-rajkeshav/events{/privacy}",
                "received_events_url": "https://api.github.com/users/itz-rajkeshav/received_events",
                "type": "User",
                "user_view_type": "public",
                "site_admin": false
            },
            "committer": {
                "login": "itz-rajkeshav",
                "id": 150330725,
                "node_id": "U_kgDOCPXdZQ",
                "avatar_url": "https://avatars.githubusercontent.com/u/150330725?v=4",
                "gravatar_id": "",
                "url": "https://api.github.com/users/itz-rajkeshav",
                "html_url": "https://github.com/itz-rajkeshav",
                "followers_url": "https://api.github.com/users/itz-rajkeshav/followers",
                "following_url": "https://api.github.com/users/itz-rajkeshav/following{/other_user}",
                "gists_url": "https://api.github.com/users/itz-rajkeshav/gists{/gist_id}",
                "starred_url": "https://api.github.com/users/itz-rajkeshav/starred{/owner}{/repo}",
                "subscriptions_url": "https://api.github.com/users/itz-rajkeshav/subscriptions",
                "organizations_url": "https://api.github.com/users/itz-rajkeshav/orgs",
                "repos_url": "https://api.github.com/users/itz-rajkeshav/repos",
                "events_url": "https://api.github.com/users/itz-rajkeshav/events{/privacy}",
                "received_events_url": "https://api.github.com/users/itz-rajkeshav/received_events",
                "type": "User",
                "user_view_type": "public",
                "site_admin": false
            },
            "parents": [
                {
                    "sha": "29f62380b9e7e4a2ce71b93623081cee79324067",
                    "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/commits/29f62380b9e7e4a2ce71b93623081cee79324067",
                    "html_url": "https://github.com/itz-rajkeshav/Keshav.dev/commit/29f62380b9e7e4a2ce71b93623081cee79324067"
                }
            ]
        },
        {
            "sha": "29f62380b9e7e4a2ce71b93623081cee79324067",
            "node_id": "C_kwDOOeh35toAKDI5ZjYyMzgwYjllN2U0YTJjZTcxYjkzNjIzMDgxY2VlNzkzMjQwNjc",
            "commit": {
                "author": {
                    "name": "Keshav raj",
                    "email": "<EMAIL>",
                    "date": "2025-04-29T20:13:53Z"
                },
                "committer": {
                    "name": "Keshav raj",
                    "email": "<EMAIL>",
                    "date": "2025-04-29T20:13:53Z"
                },
                "message": "update",
                "tree": {
                    "sha": "8c76cee8e6322ba21dd5618f27902674616b3185",
                    "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/git/trees/8c76cee8e6322ba21dd5618f27902674616b3185"
                },
                "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/git/commits/29f62380b9e7e4a2ce71b93623081cee79324067",
                "comment_count": 0,
                "verification": {
                    "verified": false,
                    "reason": "unsigned",
                    "signature": null,
                    "payload": null,
                    "verified_at": null
                }
            },
            "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/commits/29f62380b9e7e4a2ce71b93623081cee79324067",
            "html_url": "https://github.com/itz-rajkeshav/Keshav.dev/commit/29f62380b9e7e4a2ce71b93623081cee79324067",
            "comments_url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/commits/29f62380b9e7e4a2ce71b93623081cee79324067/comments",
            "author": {
                "login": "itz-rajkeshav",
                "id": 150330725,
                "node_id": "U_kgDOCPXdZQ",
                "avatar_url": "https://avatars.githubusercontent.com/u/150330725?v=4",
                "gravatar_id": "",
                "url": "https://api.github.com/users/itz-rajkeshav",
                "html_url": "https://github.com/itz-rajkeshav",
                "followers_url": "https://api.github.com/users/itz-rajkeshav/followers",
                "following_url": "https://api.github.com/users/itz-rajkeshav/following{/other_user}",
                "gists_url": "https://api.github.com/users/itz-rajkeshav/gists{/gist_id}",
                "starred_url": "https://api.github.com/users/itz-rajkeshav/starred{/owner}{/repo}",
                "subscriptions_url": "https://api.github.com/users/itz-rajkeshav/subscriptions",
                "organizations_url": "https://api.github.com/users/itz-rajkeshav/orgs",
                "repos_url": "https://api.github.com/users/itz-rajkeshav/repos",
                "events_url": "https://api.github.com/users/itz-rajkeshav/events{/privacy}",
                "received_events_url": "https://api.github.com/users/itz-rajkeshav/received_events",
                "type": "User",
                "user_view_type": "public",
                "site_admin": false
            },
            "committer": {
                "login": "itz-rajkeshav",
                "id": 150330725,
                "node_id": "U_kgDOCPXdZQ",
                "avatar_url": "https://avatars.githubusercontent.com/u/150330725?v=4",
                "gravatar_id": "",
                "url": "https://api.github.com/users/itz-rajkeshav",
                "html_url": "https://github.com/itz-rajkeshav",
                "followers_url": "https://api.github.com/users/itz-rajkeshav/followers",
                "following_url": "https://api.github.com/users/itz-rajkeshav/following{/other_user}",
                "gists_url": "https://api.github.com/users/itz-rajkeshav/gists{/gist_id}",
                "starred_url": "https://api.github.com/users/itz-rajkeshav/starred{/owner}{/repo}",
                "subscriptions_url": "https://api.github.com/users/itz-rajkeshav/subscriptions",
                "organizations_url": "https://api.github.com/users/itz-rajkeshav/orgs",
                "repos_url": "https://api.github.com/users/itz-rajkeshav/repos",
                "events_url": "https://api.github.com/users/itz-rajkeshav/events{/privacy}",
                "received_events_url": "https://api.github.com/users/itz-rajkeshav/received_events",
                "type": "User",
                "user_view_type": "public",
                "site_admin": false
            },
            "parents": [
                {
                    "sha": "111b7e999a61317f403dfe249081b827cec97b62",
                    "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/commits/111b7e999a61317f403dfe249081b827cec97b62",
                    "html_url": "https://github.com/itz-rajkeshav/Keshav.dev/commit/111b7e999a61317f403dfe249081b827cec97b62"
                }
            ]
        },
        {
            "sha": "111b7e999a61317f403dfe249081b827cec97b62",
            "node_id": "C_kwDOOeh35toAKDExMWI3ZTk5OWE2MTMxN2Y0MDNkZmUyNDkwODFiODI3Y2VjOTdiNjI",
            "commit": {
                "author": {
                    "name": "Keshav raj",
                    "email": "<EMAIL>",
                    "date": "2025-04-28T16:34:38Z"
                },
                "committer": {
                    "name": "Keshav raj",
                    "email": "<EMAIL>",
                    "date": "2025-04-28T16:34:38Z"
                },
                "message": "fix: downgrade React and TypeScript types to maintain compatibility",
                "tree": {
                    "sha": "1f23d4a9577e1c92b8ba1b1860fd3f15af40c42e",
                    "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/git/trees/1f23d4a9577e1c92b8ba1b1860fd3f15af40c42e"
                },
                "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/git/commits/111b7e999a61317f403dfe249081b827cec97b62",
                "comment_count": 0,
                "verification": {
                    "verified": false,
                    "reason": "unsigned",
                    "signature": null,
                    "payload": null,
                    "verified_at": null
                }
            },
            "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/commits/111b7e999a61317f403dfe249081b827cec97b62",
            "html_url": "https://github.com/itz-rajkeshav/Keshav.dev/commit/111b7e999a61317f403dfe249081b827cec97b62",
            "comments_url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/commits/111b7e999a61317f403dfe249081b827cec97b62/comments",
            "author": {
                "login": "itz-rajkeshav",
                "id": 150330725,
                "node_id": "U_kgDOCPXdZQ",
                "avatar_url": "https://avatars.githubusercontent.com/u/150330725?v=4",
                "gravatar_id": "",
                "url": "https://api.github.com/users/itz-rajkeshav",
                "html_url": "https://github.com/itz-rajkeshav",
                "followers_url": "https://api.github.com/users/itz-rajkeshav/followers",
                "following_url": "https://api.github.com/users/itz-rajkeshav/following{/other_user}",
                "gists_url": "https://api.github.com/users/itz-rajkeshav/gists{/gist_id}",
                "starred_url": "https://api.github.com/users/itz-rajkeshav/starred{/owner}{/repo}",
                "subscriptions_url": "https://api.github.com/users/itz-rajkeshav/subscriptions",
                "organizations_url": "https://api.github.com/users/itz-rajkeshav/orgs",
                "repos_url": "https://api.github.com/users/itz-rajkeshav/repos",
                "events_url": "https://api.github.com/users/itz-rajkeshav/events{/privacy}",
                "received_events_url": "https://api.github.com/users/itz-rajkeshav/received_events",
                "type": "User",
                "user_view_type": "public",
                "site_admin": false
            },
            "committer": {
                "login": "itz-rajkeshav",
                "id": 150330725,
                "node_id": "U_kgDOCPXdZQ",
                "avatar_url": "https://avatars.githubusercontent.com/u/150330725?v=4",
                "gravatar_id": "",
                "url": "https://api.github.com/users/itz-rajkeshav",
                "html_url": "https://github.com/itz-rajkeshav",
                "followers_url": "https://api.github.com/users/itz-rajkeshav/followers",
                "following_url": "https://api.github.com/users/itz-rajkeshav/following{/other_user}",
                "gists_url": "https://api.github.com/users/itz-rajkeshav/gists{/gist_id}",
                "starred_url": "https://api.github.com/users/itz-rajkeshav/starred{/owner}{/repo}",
                "subscriptions_url": "https://api.github.com/users/itz-rajkeshav/subscriptions",
                "organizations_url": "https://api.github.com/users/itz-rajkeshav/orgs",
                "repos_url": "https://api.github.com/users/itz-rajkeshav/repos",
                "events_url": "https://api.github.com/users/itz-rajkeshav/events{/privacy}",
                "received_events_url": "https://api.github.com/users/itz-rajkeshav/received_events",
                "type": "User",
                "user_view_type": "public",
                "site_admin": false
            },
            "parents": [
                {
                    "sha": "186678b01231ce6259d75be81b54dfcd5a7ede8b",
                    "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/commits/186678b01231ce6259d75be81b54dfcd5a7ede8b",
                    "html_url": "https://github.com/itz-rajkeshav/Keshav.dev/commit/186678b01231ce6259d75be81b54dfcd5a7ede8b"
                }
            ]
        },
        {
            "sha": "186678b01231ce6259d75be81b54dfcd5a7ede8b",
            "node_id": "C_kwDOOeh35toAKDE4NjY3OGIwMTIzMWNlNjI1OWQ3NWJlODFiNTRkZmNkNWE3ZWRlOGI",
            "commit": {
                "author": {
                    "name": "Keshav raj",
                    "email": "<EMAIL>",
                    "date": "2025-04-28T16:05:55Z"
                },
                "committer": {
                    "name": "Keshav raj",
                    "email": "<EMAIL>",
                    "date": "2025-04-28T16:05:55Z"
                },
                "message": "refactor: remove unused @emoji-mart",
                "tree": {
                    "sha": "be332091e895874247e07ed19c5645f3b6eb99de",
                    "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/git/trees/be332091e895874247e07ed19c5645f3b6eb99de"
                },
                "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/git/commits/186678b01231ce6259d75be81b54dfcd5a7ede8b",
                "comment_count": 0,
                "verification": {
                    "verified": false,
                    "reason": "unsigned",
                    "signature": null,
                    "payload": null,
                    "verified_at": null
                }
            },
            "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/commits/186678b01231ce6259d75be81b54dfcd5a7ede8b",
            "html_url": "https://github.com/itz-rajkeshav/Keshav.dev/commit/186678b01231ce6259d75be81b54dfcd5a7ede8b",
            "comments_url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/commits/186678b01231ce6259d75be81b54dfcd5a7ede8b/comments",
            "author": {
                "login": "itz-rajkeshav",
                "id": 150330725,
                "node_id": "U_kgDOCPXdZQ",
                "avatar_url": "https://avatars.githubusercontent.com/u/150330725?v=4",
                "gravatar_id": "",
                "url": "https://api.github.com/users/itz-rajkeshav",
                "html_url": "https://github.com/itz-rajkeshav",
                "followers_url": "https://api.github.com/users/itz-rajkeshav/followers",
                "following_url": "https://api.github.com/users/itz-rajkeshav/following{/other_user}",
                "gists_url": "https://api.github.com/users/itz-rajkeshav/gists{/gist_id}",
                "starred_url": "https://api.github.com/users/itz-rajkeshav/starred{/owner}{/repo}",
                "subscriptions_url": "https://api.github.com/users/itz-rajkeshav/subscriptions",
                "organizations_url": "https://api.github.com/users/itz-rajkeshav/orgs",
                "repos_url": "https://api.github.com/users/itz-rajkeshav/repos",
                "events_url": "https://api.github.com/users/itz-rajkeshav/events{/privacy}",
                "received_events_url": "https://api.github.com/users/itz-rajkeshav/received_events",
                "type": "User",
                "user_view_type": "public",
                "site_admin": false
            },
            "committer": {
                "login": "itz-rajkeshav",
                "id": 150330725,
                "node_id": "U_kgDOCPXdZQ",
                "avatar_url": "https://avatars.githubusercontent.com/u/150330725?v=4",
                "gravatar_id": "",
                "url": "https://api.github.com/users/itz-rajkeshav",
                "html_url": "https://github.com/itz-rajkeshav",
                "followers_url": "https://api.github.com/users/itz-rajkeshav/followers",
                "following_url": "https://api.github.com/users/itz-rajkeshav/following{/other_user}",
                "gists_url": "https://api.github.com/users/itz-rajkeshav/gists{/gist_id}",
                "starred_url": "https://api.github.com/users/itz-rajkeshav/starred{/owner}{/repo}",
                "subscriptions_url": "https://api.github.com/users/itz-rajkeshav/subscriptions",
                "organizations_url": "https://api.github.com/users/itz-rajkeshav/orgs",
                "repos_url": "https://api.github.com/users/itz-rajkeshav/repos",
                "events_url": "https://api.github.com/users/itz-rajkeshav/events{/privacy}",
                "received_events_url": "https://api.github.com/users/itz-rajkeshav/received_events",
                "type": "User",
                "user_view_type": "public",
                "site_admin": false
            },
            "parents": [
                {
                    "sha": "89d1d41d300c0137268958ce175ba86cf01d8bba",
                    "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/commits/89d1d41d300c0137268958ce175ba86cf01d8bba",
                    "html_url": "https://github.com/itz-rajkeshav/Keshav.dev/commit/89d1d41d300c0137268958ce175ba86cf01d8bba"
                }
            ]
        },
        {
            "sha": "89d1d41d300c0137268958ce175ba86cf01d8bba",
            "node_id": "C_kwDOOeh35toAKDg5ZDFkNDFkMzAwYzAxMzcyNjg5NThjZTE3NWJhODZjZjAxZDhiYmE",
            "commit": {
                "author": {
                    "name": "Keshav raj",
                    "email": "<EMAIL>",
                    "date": "2025-04-28T15:04:19Z"
                },
                "committer": {
                    "name": "Keshav raj",
                    "email": "<EMAIL>",
                    "date": "2025-04-28T15:04:19Z"
                },
                "message": "fix aspos in the code",
                "tree": {
                    "sha": "b634cf8b995fc82ca82668c3c239f7e0bff74c29",
                    "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/git/trees/b634cf8b995fc82ca82668c3c239f7e0bff74c29"
                },
                "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/git/commits/89d1d41d300c0137268958ce175ba86cf01d8bba",
                "comment_count": 0,
                "verification": {
                    "verified": false,
                    "reason": "unsigned",
                    "signature": null,
                    "payload": null,
                    "verified_at": null
                }
            },
            "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/commits/89d1d41d300c0137268958ce175ba86cf01d8bba",
            "html_url": "https://github.com/itz-rajkeshav/Keshav.dev/commit/89d1d41d300c0137268958ce175ba86cf01d8bba",
            "comments_url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/commits/89d1d41d300c0137268958ce175ba86cf01d8bba/comments",
            "author": {
                "login": "itz-rajkeshav",
                "id": 150330725,
                "node_id": "U_kgDOCPXdZQ",
                "avatar_url": "https://avatars.githubusercontent.com/u/150330725?v=4",
                "gravatar_id": "",
                "url": "https://api.github.com/users/itz-rajkeshav",
                "html_url": "https://github.com/itz-rajkeshav",
                "followers_url": "https://api.github.com/users/itz-rajkeshav/followers",
                "following_url": "https://api.github.com/users/itz-rajkeshav/following{/other_user}",
                "gists_url": "https://api.github.com/users/itz-rajkeshav/gists{/gist_id}",
                "starred_url": "https://api.github.com/users/itz-rajkeshav/starred{/owner}{/repo}",
                "subscriptions_url": "https://api.github.com/users/itz-rajkeshav/subscriptions",
                "organizations_url": "https://api.github.com/users/itz-rajkeshav/orgs",
                "repos_url": "https://api.github.com/users/itz-rajkeshav/repos",
                "events_url": "https://api.github.com/users/itz-rajkeshav/events{/privacy}",
                "received_events_url": "https://api.github.com/users/itz-rajkeshav/received_events",
                "type": "User",
                "user_view_type": "public",
                "site_admin": false
            },
            "committer": {
                "login": "itz-rajkeshav",
                "id": 150330725,
                "node_id": "U_kgDOCPXdZQ",
                "avatar_url": "https://avatars.githubusercontent.com/u/150330725?v=4",
                "gravatar_id": "",
                "url": "https://api.github.com/users/itz-rajkeshav",
                "html_url": "https://github.com/itz-rajkeshav",
                "followers_url": "https://api.github.com/users/itz-rajkeshav/followers",
                "following_url": "https://api.github.com/users/itz-rajkeshav/following{/other_user}",
                "gists_url": "https://api.github.com/users/itz-rajkeshav/gists{/gist_id}",
                "starred_url": "https://api.github.com/users/itz-rajkeshav/starred{/owner}{/repo}",
                "subscriptions_url": "https://api.github.com/users/itz-rajkeshav/subscriptions",
                "organizations_url": "https://api.github.com/users/itz-rajkeshav/orgs",
                "repos_url": "https://api.github.com/users/itz-rajkeshav/repos",
                "events_url": "https://api.github.com/users/itz-rajkeshav/events{/privacy}",
                "received_events_url": "https://api.github.com/users/itz-rajkeshav/received_events",
                "type": "User",
                "user_view_type": "public",
                "site_admin": false
            },
            "parents": [
                {
                    "sha": "081ac86519fb1a33fb49f53e2420d9d6056f54c5",
                    "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/commits/081ac86519fb1a33fb49f53e2420d9d6056f54c5",
                    "html_url": "https://github.com/itz-rajkeshav/Keshav.dev/commit/081ac86519fb1a33fb49f53e2420d9d6056f54c5"
                }
            ]
        },
        {
            "sha": "081ac86519fb1a33fb49f53e2420d9d6056f54c5",
            "node_id": "C_kwDOOeh35toAKDA4MWFjODY1MTlmYjFhMzNmYjQ5ZjUzZTI0MjBkOWQ2MDU2ZjU0YzU",
            "commit": {
                "author": {
                    "name": "Keshav raj",
                    "email": "<EMAIL>",
                    "date": "2025-04-28T15:02:43Z"
                },
                "committer": {
                    "name": "Keshav raj",
                    "email": "<EMAIL>",
                    "date": "2025-04-28T15:02:43Z"
                },
                "message": " add dotenv dependency and refactor AnonymousWall component",
                "tree": {
                    "sha": "7741d67bb07c46e0ba3dd7334c11f1dfc593e7c7",
                    "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/git/trees/7741d67bb07c46e0ba3dd7334c11f1dfc593e7c7"
                },
                "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/git/commits/081ac86519fb1a33fb49f53e2420d9d6056f54c5",
                "comment_count": 0,
                "verification": {
                    "verified": false,
                    "reason": "unsigned",
                    "signature": null,
                    "payload": null,
                    "verified_at": null
                }
            },
            "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/commits/081ac86519fb1a33fb49f53e2420d9d6056f54c5",
            "html_url": "https://github.com/itz-rajkeshav/Keshav.dev/commit/081ac86519fb1a33fb49f53e2420d9d6056f54c5",
            "comments_url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/commits/081ac86519fb1a33fb49f53e2420d9d6056f54c5/comments",
            "author": {
                "login": "itz-rajkeshav",
                "id": 150330725,
                "node_id": "U_kgDOCPXdZQ",
                "avatar_url": "https://avatars.githubusercontent.com/u/150330725?v=4",
                "gravatar_id": "",
                "url": "https://api.github.com/users/itz-rajkeshav",
                "html_url": "https://github.com/itz-rajkeshav",
                "followers_url": "https://api.github.com/users/itz-rajkeshav/followers",
                "following_url": "https://api.github.com/users/itz-rajkeshav/following{/other_user}",
                "gists_url": "https://api.github.com/users/itz-rajkeshav/gists{/gist_id}",
                "starred_url": "https://api.github.com/users/itz-rajkeshav/starred{/owner}{/repo}",
                "subscriptions_url": "https://api.github.com/users/itz-rajkeshav/subscriptions",
                "organizations_url": "https://api.github.com/users/itz-rajkeshav/orgs",
                "repos_url": "https://api.github.com/users/itz-rajkeshav/repos",
                "events_url": "https://api.github.com/users/itz-rajkeshav/events{/privacy}",
                "received_events_url": "https://api.github.com/users/itz-rajkeshav/received_events",
                "type": "User",
                "user_view_type": "public",
                "site_admin": false
            },
            "committer": {
                "login": "itz-rajkeshav",
                "id": 150330725,
                "node_id": "U_kgDOCPXdZQ",
                "avatar_url": "https://avatars.githubusercontent.com/u/150330725?v=4",
                "gravatar_id": "",
                "url": "https://api.github.com/users/itz-rajkeshav",
                "html_url": "https://github.com/itz-rajkeshav",
                "followers_url": "https://api.github.com/users/itz-rajkeshav/followers",
                "following_url": "https://api.github.com/users/itz-rajkeshav/following{/other_user}",
                "gists_url": "https://api.github.com/users/itz-rajkeshav/gists{/gist_id}",
                "starred_url": "https://api.github.com/users/itz-rajkeshav/starred{/owner}{/repo}",
                "subscriptions_url": "https://api.github.com/users/itz-rajkeshav/subscriptions",
                "organizations_url": "https://api.github.com/users/itz-rajkeshav/orgs",
                "repos_url": "https://api.github.com/users/itz-rajkeshav/repos",
                "events_url": "https://api.github.com/users/itz-rajkeshav/events{/privacy}",
                "received_events_url": "https://api.github.com/users/itz-rajkeshav/received_events",
                "type": "User",
                "user_view_type": "public",
                "site_admin": false
            },
            "parents": [
                {
                    "sha": "972084d64824c9c350c727289f628f227b27064a",
                    "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/commits/972084d64824c9c350c727289f628f227b27064a",
                    "html_url": "https://github.com/itz-rajkeshav/Keshav.dev/commit/972084d64824c9c350c727289f628f227b27064a"
                }
            ]
        },
        {
            "sha": "972084d64824c9c350c727289f628f227b27064a",
            "node_id": "C_kwDOOeh35toAKDk3MjA4NGQ2NDgyNGM5YzM1MGM3MjcyODlmNjI4ZjIyN2IyNzA2NGE",
            "commit": {
                "author": {
                    "name": "Keshav raj",
                    "email": "<EMAIL>",
                    "date": "2025-04-28T14:43:25Z"
                },
                "committer": {
                    "name": "Keshav raj",
                    "email": "<EMAIL>",
                    "date": "2025-04-28T14:43:25Z"
                },
                "message": "feat: add ThirdPage component showcasing projects with detailed descriptions and technologies used\n\nfeat: implement animated GithubIcon component with hover effects\n\nfeat: create animated InstagramIcon component with interactive touch and mouse events\n\nfeat: develop animated LinkedinIcon component with responsive animations\n\nfeat: introduce MailCheckIcon component with animation on hover for email verification",
                "tree": {
                    "sha": "757cea000c7799d7c1e4f9851f284ad4f2555057",
                    "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/git/trees/757cea000c7799d7c1e4f9851f284ad4f2555057"
                },
                "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/git/commits/972084d64824c9c350c727289f628f227b27064a",
                "comment_count": 0,
                "verification": {
                    "verified": false,
                    "reason": "unsigned",
                    "signature": null,
                    "payload": null,
                    "verified_at": null
                }
            },
            "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/commits/972084d64824c9c350c727289f628f227b27064a",
            "html_url": "https://github.com/itz-rajkeshav/Keshav.dev/commit/972084d64824c9c350c727289f628f227b27064a",
            "comments_url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/commits/972084d64824c9c350c727289f628f227b27064a/comments",
            "author": {
                "login": "itz-rajkeshav",
                "id": 150330725,
                "node_id": "U_kgDOCPXdZQ",
                "avatar_url": "https://avatars.githubusercontent.com/u/150330725?v=4",
                "gravatar_id": "",
                "url": "https://api.github.com/users/itz-rajkeshav",
                "html_url": "https://github.com/itz-rajkeshav",
                "followers_url": "https://api.github.com/users/itz-rajkeshav/followers",
                "following_url": "https://api.github.com/users/itz-rajkeshav/following{/other_user}",
                "gists_url": "https://api.github.com/users/itz-rajkeshav/gists{/gist_id}",
                "starred_url": "https://api.github.com/users/itz-rajkeshav/starred{/owner}{/repo}",
                "subscriptions_url": "https://api.github.com/users/itz-rajkeshav/subscriptions",
                "organizations_url": "https://api.github.com/users/itz-rajkeshav/orgs",
                "repos_url": "https://api.github.com/users/itz-rajkeshav/repos",
                "events_url": "https://api.github.com/users/itz-rajkeshav/events{/privacy}",
                "received_events_url": "https://api.github.com/users/itz-rajkeshav/received_events",
                "type": "User",
                "user_view_type": "public",
                "site_admin": false
            },
            "committer": {
                "login": "itz-rajkeshav",
                "id": 150330725,
                "node_id": "U_kgDOCPXdZQ",
                "avatar_url": "https://avatars.githubusercontent.com/u/150330725?v=4",
                "gravatar_id": "",
                "url": "https://api.github.com/users/itz-rajkeshav",
                "html_url": "https://github.com/itz-rajkeshav",
                "followers_url": "https://api.github.com/users/itz-rajkeshav/followers",
                "following_url": "https://api.github.com/users/itz-rajkeshav/following{/other_user}",
                "gists_url": "https://api.github.com/users/itz-rajkeshav/gists{/gist_id}",
                "starred_url": "https://api.github.com/users/itz-rajkeshav/starred{/owner}{/repo}",
                "subscriptions_url": "https://api.github.com/users/itz-rajkeshav/subscriptions",
                "organizations_url": "https://api.github.com/users/itz-rajkeshav/orgs",
                "repos_url": "https://api.github.com/users/itz-rajkeshav/repos",
                "events_url": "https://api.github.com/users/itz-rajkeshav/events{/privacy}",
                "received_events_url": "https://api.github.com/users/itz-rajkeshav/received_events",
                "type": "User",
                "user_view_type": "public",
                "site_admin": false
            },
            "parents": [
                {
                    "sha": "b3beea7d0a6c07226dd8273a1e32ff24aa9ac149",
                    "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/commits/b3beea7d0a6c07226dd8273a1e32ff24aa9ac149",
                    "html_url": "https://github.com/itz-rajkeshav/Keshav.dev/commit/b3beea7d0a6c07226dd8273a1e32ff24aa9ac149"
                }
            ]
        },
        {
            "sha": "b3beea7d0a6c07226dd8273a1e32ff24aa9ac149",
            "node_id": "C_kwDOOeh35toAKGIzYmVlYTdkMGE2YzA3MjI2ZGQ4MjczYTFlMzJmZjI0YWE5YWMxNDk",
            "commit": {
                "author": {
                    "name": "Keshav raj",
                    "email": "<EMAIL>",
                    "date": "2025-04-26T16:15:47Z"
                },
                "committer": {
                    "name": "Keshav raj",
                    "email": "<EMAIL>",
                    "date": "2025-04-26T16:15:47Z"
                },
                "message": "make the professional section and fix rthe splash screen",
                "tree": {
                    "sha": "382c32d0abbd4e65fbeb2051e5b01d66b1a9899c",
                    "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/git/trees/382c32d0abbd4e65fbeb2051e5b01d66b1a9899c"
                },
                "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/git/commits/b3beea7d0a6c07226dd8273a1e32ff24aa9ac149",
                "comment_count": 0,
                "verification": {
                    "verified": false,
                    "reason": "unsigned",
                    "signature": null,
                    "payload": null,
                    "verified_at": null
                }
            },
            "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/commits/b3beea7d0a6c07226dd8273a1e32ff24aa9ac149",
            "html_url": "https://github.com/itz-rajkeshav/Keshav.dev/commit/b3beea7d0a6c07226dd8273a1e32ff24aa9ac149",
            "comments_url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/commits/b3beea7d0a6c07226dd8273a1e32ff24aa9ac149/comments",
            "author": {
                "login": "itz-rajkeshav",
                "id": 150330725,
                "node_id": "U_kgDOCPXdZQ",
                "avatar_url": "https://avatars.githubusercontent.com/u/150330725?v=4",
                "gravatar_id": "",
                "url": "https://api.github.com/users/itz-rajkeshav",
                "html_url": "https://github.com/itz-rajkeshav",
                "followers_url": "https://api.github.com/users/itz-rajkeshav/followers",
                "following_url": "https://api.github.com/users/itz-rajkeshav/following{/other_user}",
                "gists_url": "https://api.github.com/users/itz-rajkeshav/gists{/gist_id}",
                "starred_url": "https://api.github.com/users/itz-rajkeshav/starred{/owner}{/repo}",
                "subscriptions_url": "https://api.github.com/users/itz-rajkeshav/subscriptions",
                "organizations_url": "https://api.github.com/users/itz-rajkeshav/orgs",
                "repos_url": "https://api.github.com/users/itz-rajkeshav/repos",
                "events_url": "https://api.github.com/users/itz-rajkeshav/events{/privacy}",
                "received_events_url": "https://api.github.com/users/itz-rajkeshav/received_events",
                "type": "User",
                "user_view_type": "public",
                "site_admin": false
            },
            "committer": {
                "login": "itz-rajkeshav",
                "id": 150330725,
                "node_id": "U_kgDOCPXdZQ",
                "avatar_url": "https://avatars.githubusercontent.com/u/150330725?v=4",
                "gravatar_id": "",
                "url": "https://api.github.com/users/itz-rajkeshav",
                "html_url": "https://github.com/itz-rajkeshav",
                "followers_url": "https://api.github.com/users/itz-rajkeshav/followers",
                "following_url": "https://api.github.com/users/itz-rajkeshav/following{/other_user}",
                "gists_url": "https://api.github.com/users/itz-rajkeshav/gists{/gist_id}",
                "starred_url": "https://api.github.com/users/itz-rajkeshav/starred{/owner}{/repo}",
                "subscriptions_url": "https://api.github.com/users/itz-rajkeshav/subscriptions",
                "organizations_url": "https://api.github.com/users/itz-rajkeshav/orgs",
                "repos_url": "https://api.github.com/users/itz-rajkeshav/repos",
                "events_url": "https://api.github.com/users/itz-rajkeshav/events{/privacy}",
                "received_events_url": "https://api.github.com/users/itz-rajkeshav/received_events",
                "type": "User",
                "user_view_type": "public",
                "site_admin": false
            },
            "parents": [
                {
                    "sha": "a7290f4766a7c0ba7286c7aeb53c3ddacc2a8456",
                    "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/commits/a7290f4766a7c0ba7286c7aeb53c3ddacc2a8456",
                    "html_url": "https://github.com/itz-rajkeshav/Keshav.dev/commit/a7290f4766a7c0ba7286c7aeb53c3ddacc2a8456"
                }
            ]
        },
        {
            "sha": "a7290f4766a7c0ba7286c7aeb53c3ddacc2a8456",
            "node_id": "C_kwDOOeh35toAKGE3MjkwZjQ3NjZhN2MwYmE3Mjg2YzdhZWI1M2MzZGRhY2MyYTg0NTY",
            "commit": {
                "author": {
                    "name": "Keshav raj",
                    "email": "<EMAIL>",
                    "date": "2025-04-26T12:38:46Z"
                },
                "committer": {
                    "name": "Keshav raj",
                    "email": "<EMAIL>",
                    "date": "2025-04-26T12:38:46Z"
                },
                "message": " add the splash cursor and make fully responsive",
                "tree": {
                    "sha": "6ee891b186ac8c48379402e79333b77c98d6e30c",
                    "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/git/trees/6ee891b186ac8c48379402e79333b77c98d6e30c"
                },
                "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/git/commits/a7290f4766a7c0ba7286c7aeb53c3ddacc2a8456",
                "comment_count": 0,
                "verification": {
                    "verified": false,
                    "reason": "unsigned",
                    "signature": null,
                    "payload": null,
                    "verified_at": null
                }
            },
            "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/commits/a7290f4766a7c0ba7286c7aeb53c3ddacc2a8456",
            "html_url": "https://github.com/itz-rajkeshav/Keshav.dev/commit/a7290f4766a7c0ba7286c7aeb53c3ddacc2a8456",
            "comments_url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/commits/a7290f4766a7c0ba7286c7aeb53c3ddacc2a8456/comments",
            "author": {
                "login": "itz-rajkeshav",
                "id": 150330725,
                "node_id": "U_kgDOCPXdZQ",
                "avatar_url": "https://avatars.githubusercontent.com/u/150330725?v=4",
                "gravatar_id": "",
                "url": "https://api.github.com/users/itz-rajkeshav",
                "html_url": "https://github.com/itz-rajkeshav",
                "followers_url": "https://api.github.com/users/itz-rajkeshav/followers",
                "following_url": "https://api.github.com/users/itz-rajkeshav/following{/other_user}",
                "gists_url": "https://api.github.com/users/itz-rajkeshav/gists{/gist_id}",
                "starred_url": "https://api.github.com/users/itz-rajkeshav/starred{/owner}{/repo}",
                "subscriptions_url": "https://api.github.com/users/itz-rajkeshav/subscriptions",
                "organizations_url": "https://api.github.com/users/itz-rajkeshav/orgs",
                "repos_url": "https://api.github.com/users/itz-rajkeshav/repos",
                "events_url": "https://api.github.com/users/itz-rajkeshav/events{/privacy}",
                "received_events_url": "https://api.github.com/users/itz-rajkeshav/received_events",
                "type": "User",
                "user_view_type": "public",
                "site_admin": false
            },
            "committer": {
                "login": "itz-rajkeshav",
                "id": 150330725,
                "node_id": "U_kgDOCPXdZQ",
                "avatar_url": "https://avatars.githubusercontent.com/u/150330725?v=4",
                "gravatar_id": "",
                "url": "https://api.github.com/users/itz-rajkeshav",
                "html_url": "https://github.com/itz-rajkeshav",
                "followers_url": "https://api.github.com/users/itz-rajkeshav/followers",
                "following_url": "https://api.github.com/users/itz-rajkeshav/following{/other_user}",
                "gists_url": "https://api.github.com/users/itz-rajkeshav/gists{/gist_id}",
                "starred_url": "https://api.github.com/users/itz-rajkeshav/starred{/owner}{/repo}",
                "subscriptions_url": "https://api.github.com/users/itz-rajkeshav/subscriptions",
                "organizations_url": "https://api.github.com/users/itz-rajkeshav/orgs",
                "repos_url": "https://api.github.com/users/itz-rajkeshav/repos",
                "events_url": "https://api.github.com/users/itz-rajkeshav/events{/privacy}",
                "received_events_url": "https://api.github.com/users/itz-rajkeshav/received_events",
                "type": "User",
                "user_view_type": "public",
                "site_admin": false
            },
            "parents": [
                {
                    "sha": "973466c3fe649865d5e8920ee0ad60d253d2bee5",
                    "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/commits/973466c3fe649865d5e8920ee0ad60d253d2bee5",
                    "html_url": "https://github.com/itz-rajkeshav/Keshav.dev/commit/973466c3fe649865d5e8920ee0ad60d253d2bee5"
                }
            ]
        },
        {
            "sha": "973466c3fe649865d5e8920ee0ad60d253d2bee5",
            "node_id": "C_kwDOOeh35toAKDk3MzQ2NmMzZmU2NDk4NjVkNWU4OTIwZWUwYWQ2MGQyNTNkMmJlZTU",
            "commit": {
                "author": {
                    "name": "Keshav raj",
                    "email": "<EMAIL>",
                    "date": "2025-04-25T19:09:09Z"
                },
                "committer": {
                    "name": "Keshav raj",
                    "email": "<EMAIL>",
                    "date": "2025-04-25T19:09:09Z"
                },
                "message": "responsive v1",
                "tree": {
                    "sha": "3bee147b4fcb8caada7c1493d33507d6c2cfa5b0",
                    "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/git/trees/3bee147b4fcb8caada7c1493d33507d6c2cfa5b0"
                },
                "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/git/commits/973466c3fe649865d5e8920ee0ad60d253d2bee5",
                "comment_count": 0,
                "verification": {
                    "verified": false,
                    "reason": "unsigned",
                    "signature": null,
                    "payload": null,
                    "verified_at": null
                }
            },
            "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/commits/973466c3fe649865d5e8920ee0ad60d253d2bee5",
            "html_url": "https://github.com/itz-rajkeshav/Keshav.dev/commit/973466c3fe649865d5e8920ee0ad60d253d2bee5",
            "comments_url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/commits/973466c3fe649865d5e8920ee0ad60d253d2bee5/comments",
            "author": {
                "login": "itz-rajkeshav",
                "id": 150330725,
                "node_id": "U_kgDOCPXdZQ",
                "avatar_url": "https://avatars.githubusercontent.com/u/150330725?v=4",
                "gravatar_id": "",
                "url": "https://api.github.com/users/itz-rajkeshav",
                "html_url": "https://github.com/itz-rajkeshav",
                "followers_url": "https://api.github.com/users/itz-rajkeshav/followers",
                "following_url": "https://api.github.com/users/itz-rajkeshav/following{/other_user}",
                "gists_url": "https://api.github.com/users/itz-rajkeshav/gists{/gist_id}",
                "starred_url": "https://api.github.com/users/itz-rajkeshav/starred{/owner}{/repo}",
                "subscriptions_url": "https://api.github.com/users/itz-rajkeshav/subscriptions",
                "organizations_url": "https://api.github.com/users/itz-rajkeshav/orgs",
                "repos_url": "https://api.github.com/users/itz-rajkeshav/repos",
                "events_url": "https://api.github.com/users/itz-rajkeshav/events{/privacy}",
                "received_events_url": "https://api.github.com/users/itz-rajkeshav/received_events",
                "type": "User",
                "user_view_type": "public",
                "site_admin": false
            },
            "committer": {
                "login": "itz-rajkeshav",
                "id": 150330725,
                "node_id": "U_kgDOCPXdZQ",
                "avatar_url": "https://avatars.githubusercontent.com/u/150330725?v=4",
                "gravatar_id": "",
                "url": "https://api.github.com/users/itz-rajkeshav",
                "html_url": "https://github.com/itz-rajkeshav",
                "followers_url": "https://api.github.com/users/itz-rajkeshav/followers",
                "following_url": "https://api.github.com/users/itz-rajkeshav/following{/other_user}",
                "gists_url": "https://api.github.com/users/itz-rajkeshav/gists{/gist_id}",
                "starred_url": "https://api.github.com/users/itz-rajkeshav/starred{/owner}{/repo}",
                "subscriptions_url": "https://api.github.com/users/itz-rajkeshav/subscriptions",
                "organizations_url": "https://api.github.com/users/itz-rajkeshav/orgs",
                "repos_url": "https://api.github.com/users/itz-rajkeshav/repos",
                "events_url": "https://api.github.com/users/itz-rajkeshav/events{/privacy}",
                "received_events_url": "https://api.github.com/users/itz-rajkeshav/received_events",
                "type": "User",
                "user_view_type": "public",
                "site_admin": false
            },
            "parents": [
                {
                    "sha": "f9704682f9de8adc8f2c0a1d6f4fdca002e524a1",
                    "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/commits/f9704682f9de8adc8f2c0a1d6f4fdca002e524a1",
                    "html_url": "https://github.com/itz-rajkeshav/Keshav.dev/commit/f9704682f9de8adc8f2c0a1d6f4fdca002e524a1"
                }
            ]
        },
        {
            "sha": "f9704682f9de8adc8f2c0a1d6f4fdca002e524a1",
            "node_id": "C_kwDOOeh35toAKGY5NzA0NjgyZjlkZThhZGM4ZjJjMGExZDZmNGZkY2EwMDJlNTI0YTE",
            "commit": {
                "author": {
                    "name": "Keshav raj",
                    "email": "<EMAIL>",
                    "date": "2025-04-25T18:10:59Z"
                },
                "committer": {
                    "name": "Keshav raj",
                    "email": "<EMAIL>",
                    "date": "2025-04-25T18:10:59Z"
                },
                "message": " implement second page and update",
                "tree": {
                    "sha": "b6bf5efa838c166625e18b31f35a8a1caf4b7ca3",
                    "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/git/trees/b6bf5efa838c166625e18b31f35a8a1caf4b7ca3"
                },
                "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/git/commits/f9704682f9de8adc8f2c0a1d6f4fdca002e524a1",
                "comment_count": 0,
                "verification": {
                    "verified": false,
                    "reason": "unsigned",
                    "signature": null,
                    "payload": null,
                    "verified_at": null
                }
            },
            "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/commits/f9704682f9de8adc8f2c0a1d6f4fdca002e524a1",
            "html_url": "https://github.com/itz-rajkeshav/Keshav.dev/commit/f9704682f9de8adc8f2c0a1d6f4fdca002e524a1",
            "comments_url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/commits/f9704682f9de8adc8f2c0a1d6f4fdca002e524a1/comments",
            "author": {
                "login": "itz-rajkeshav",
                "id": 150330725,
                "node_id": "U_kgDOCPXdZQ",
                "avatar_url": "https://avatars.githubusercontent.com/u/150330725?v=4",
                "gravatar_id": "",
                "url": "https://api.github.com/users/itz-rajkeshav",
                "html_url": "https://github.com/itz-rajkeshav",
                "followers_url": "https://api.github.com/users/itz-rajkeshav/followers",
                "following_url": "https://api.github.com/users/itz-rajkeshav/following{/other_user}",
                "gists_url": "https://api.github.com/users/itz-rajkeshav/gists{/gist_id}",
                "starred_url": "https://api.github.com/users/itz-rajkeshav/starred{/owner}{/repo}",
                "subscriptions_url": "https://api.github.com/users/itz-rajkeshav/subscriptions",
                "organizations_url": "https://api.github.com/users/itz-rajkeshav/orgs",
                "repos_url": "https://api.github.com/users/itz-rajkeshav/repos",
                "events_url": "https://api.github.com/users/itz-rajkeshav/events{/privacy}",
                "received_events_url": "https://api.github.com/users/itz-rajkeshav/received_events",
                "type": "User",
                "user_view_type": "public",
                "site_admin": false
            },
            "committer": {
                "login": "itz-rajkeshav",
                "id": 150330725,
                "node_id": "U_kgDOCPXdZQ",
                "avatar_url": "https://avatars.githubusercontent.com/u/150330725?v=4",
                "gravatar_id": "",
                "url": "https://api.github.com/users/itz-rajkeshav",
                "html_url": "https://github.com/itz-rajkeshav",
                "followers_url": "https://api.github.com/users/itz-rajkeshav/followers",
                "following_url": "https://api.github.com/users/itz-rajkeshav/following{/other_user}",
                "gists_url": "https://api.github.com/users/itz-rajkeshav/gists{/gist_id}",
                "starred_url": "https://api.github.com/users/itz-rajkeshav/starred{/owner}{/repo}",
                "subscriptions_url": "https://api.github.com/users/itz-rajkeshav/subscriptions",
                "organizations_url": "https://api.github.com/users/itz-rajkeshav/orgs",
                "repos_url": "https://api.github.com/users/itz-rajkeshav/repos",
                "events_url": "https://api.github.com/users/itz-rajkeshav/events{/privacy}",
                "received_events_url": "https://api.github.com/users/itz-rajkeshav/received_events",
                "type": "User",
                "user_view_type": "public",
                "site_admin": false
            },
            "parents": [
                {
                    "sha": "19bc1095f095adfb1fc8ed9cf6e8d4754c2e1463",
                    "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/commits/19bc1095f095adfb1fc8ed9cf6e8d4754c2e1463",
                    "html_url": "https://github.com/itz-rajkeshav/Keshav.dev/commit/19bc1095f095adfb1fc8ed9cf6e8d4754c2e1463"
                }
            ]
        },
        {
            "sha": "19bc1095f095adfb1fc8ed9cf6e8d4754c2e1463",
            "node_id": "C_kwDOOeh35toAKDE5YmMxMDk1ZjA5NWFkZmIxZmM4ZWQ5Y2Y2ZThkNDc1NGMyZTE0NjM",
            "commit": {
                "author": {
                    "name": "Keshav raj",
                    "email": "<EMAIL>",
                    "date": "2025-04-24T16:36:42Z"
                },
                "committer": {
                    "name": "Keshav raj",
                    "email": "<EMAIL>",
                    "date": "2025-04-24T16:36:42Z"
                },
                "message": "initialize first page",
                "tree": {
                    "sha": "383e20a680e1a42cc6fc764fc5749b3105a0df49",
                    "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/git/trees/383e20a680e1a42cc6fc764fc5749b3105a0df49"
                },
                "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/git/commits/19bc1095f095adfb1fc8ed9cf6e8d4754c2e1463",
                "comment_count": 0,
                "verification": {
                    "verified": false,
                    "reason": "unsigned",
                    "signature": null,
                    "payload": null,
                    "verified_at": null
                }
            },
            "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/commits/19bc1095f095adfb1fc8ed9cf6e8d4754c2e1463",
            "html_url": "https://github.com/itz-rajkeshav/Keshav.dev/commit/19bc1095f095adfb1fc8ed9cf6e8d4754c2e1463",
            "comments_url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/commits/19bc1095f095adfb1fc8ed9cf6e8d4754c2e1463/comments",
            "author": {
                "login": "itz-rajkeshav",
                "id": 150330725,
                "node_id": "U_kgDOCPXdZQ",
                "avatar_url": "https://avatars.githubusercontent.com/u/150330725?v=4",
                "gravatar_id": "",
                "url": "https://api.github.com/users/itz-rajkeshav",
                "html_url": "https://github.com/itz-rajkeshav",
                "followers_url": "https://api.github.com/users/itz-rajkeshav/followers",
                "following_url": "https://api.github.com/users/itz-rajkeshav/following{/other_user}",
                "gists_url": "https://api.github.com/users/itz-rajkeshav/gists{/gist_id}",
                "starred_url": "https://api.github.com/users/itz-rajkeshav/starred{/owner}{/repo}",
                "subscriptions_url": "https://api.github.com/users/itz-rajkeshav/subscriptions",
                "organizations_url": "https://api.github.com/users/itz-rajkeshav/orgs",
                "repos_url": "https://api.github.com/users/itz-rajkeshav/repos",
                "events_url": "https://api.github.com/users/itz-rajkeshav/events{/privacy}",
                "received_events_url": "https://api.github.com/users/itz-rajkeshav/received_events",
                "type": "User",
                "user_view_type": "public",
                "site_admin": false
            },
            "committer": {
                "login": "itz-rajkeshav",
                "id": 150330725,
                "node_id": "U_kgDOCPXdZQ",
                "avatar_url": "https://avatars.githubusercontent.com/u/150330725?v=4",
                "gravatar_id": "",
                "url": "https://api.github.com/users/itz-rajkeshav",
                "html_url": "https://github.com/itz-rajkeshav",
                "followers_url": "https://api.github.com/users/itz-rajkeshav/followers",
                "following_url": "https://api.github.com/users/itz-rajkeshav/following{/other_user}",
                "gists_url": "https://api.github.com/users/itz-rajkeshav/gists{/gist_id}",
                "starred_url": "https://api.github.com/users/itz-rajkeshav/starred{/owner}{/repo}",
                "subscriptions_url": "https://api.github.com/users/itz-rajkeshav/subscriptions",
                "organizations_url": "https://api.github.com/users/itz-rajkeshav/orgs",
                "repos_url": "https://api.github.com/users/itz-rajkeshav/repos",
                "events_url": "https://api.github.com/users/itz-rajkeshav/events{/privacy}",
                "received_events_url": "https://api.github.com/users/itz-rajkeshav/received_events",
                "type": "User",
                "user_view_type": "public",
                "site_admin": false
            },
            "parents": [
                {
                    "sha": "6eef99a19b60fa00896eb5852da3a27145edbabf",
                    "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/commits/6eef99a19b60fa00896eb5852da3a27145edbabf",
                    "html_url": "https://github.com/itz-rajkeshav/Keshav.dev/commit/6eef99a19b60fa00896eb5852da3a27145edbabf"
                }
            ]
        },
        {
            "sha": "6eef99a19b60fa00896eb5852da3a27145edbabf",
            "node_id": "C_kwDOOeh35toAKDZlZWY5OWExOWI2MGZhMDA4OTZlYjU4NTJkYTNhMjcxNDVlZGJhYmY",
            "commit": {
                "author": {
                    "name": "Keshav raj",
                    "email": "<EMAIL>",
                    "date": "2025-04-22T16:32:13Z"
                },
                "committer": {
                    "name": "Keshav raj",
                    "email": "<EMAIL>",
                    "date": "2025-04-22T16:32:13Z"
                },
                "message": "Initial commit from Create Next App",
                "tree": {
                    "sha": "444c5da13bc5c267c3867500033c2994d8f03c03",
                    "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/git/trees/444c5da13bc5c267c3867500033c2994d8f03c03"
                },
                "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/git/commits/6eef99a19b60fa00896eb5852da3a27145edbabf",
                "comment_count": 0,
                "verification": {
                    "verified": false,
                    "reason": "unsigned",
                    "signature": null,
                    "payload": null,
                    "verified_at": null
                }
            },
            "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/commits/6eef99a19b60fa00896eb5852da3a27145edbabf",
            "html_url": "https://github.com/itz-rajkeshav/Keshav.dev/commit/6eef99a19b60fa00896eb5852da3a27145edbabf",
            "comments_url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/commits/6eef99a19b60fa00896eb5852da3a27145edbabf/comments",
            "author": {
                "login": "itz-rajkeshav",
                "id": 150330725,
                "node_id": "U_kgDOCPXdZQ",
                "avatar_url": "https://avatars.githubusercontent.com/u/150330725?v=4",
                "gravatar_id": "",
                "url": "https://api.github.com/users/itz-rajkeshav",
                "html_url": "https://github.com/itz-rajkeshav",
                "followers_url": "https://api.github.com/users/itz-rajkeshav/followers",
                "following_url": "https://api.github.com/users/itz-rajkeshav/following{/other_user}",
                "gists_url": "https://api.github.com/users/itz-rajkeshav/gists{/gist_id}",
                "starred_url": "https://api.github.com/users/itz-rajkeshav/starred{/owner}{/repo}",
                "subscriptions_url": "https://api.github.com/users/itz-rajkeshav/subscriptions",
                "organizations_url": "https://api.github.com/users/itz-rajkeshav/orgs",
                "repos_url": "https://api.github.com/users/itz-rajkeshav/repos",
                "events_url": "https://api.github.com/users/itz-rajkeshav/events{/privacy}",
                "received_events_url": "https://api.github.com/users/itz-rajkeshav/received_events",
                "type": "User",
                "user_view_type": "public",
                "site_admin": false
            },
            "committer": {
                "login": "itz-rajkeshav",
                "id": 150330725,
                "node_id": "U_kgDOCPXdZQ",
                "avatar_url": "https://avatars.githubusercontent.com/u/150330725?v=4",
                "gravatar_id": "",
                "url": "https://api.github.com/users/itz-rajkeshav",
                "html_url": "https://github.com/itz-rajkeshav",
                "followers_url": "https://api.github.com/users/itz-rajkeshav/followers",
                "following_url": "https://api.github.com/users/itz-rajkeshav/following{/other_user}",
                "gists_url": "https://api.github.com/users/itz-rajkeshav/gists{/gist_id}",
                "starred_url": "https://api.github.com/users/itz-rajkeshav/starred{/owner}{/repo}",
                "subscriptions_url": "https://api.github.com/users/itz-rajkeshav/subscriptions",
                "organizations_url": "https://api.github.com/users/itz-rajkeshav/orgs",
                "repos_url": "https://api.github.com/users/itz-rajkeshav/repos",
                "events_url": "https://api.github.com/users/itz-rajkeshav/events{/privacy}",
                "received_events_url": "https://api.github.com/users/itz-rajkeshav/received_events",
                "type": "User",
                "user_view_type": "public",
                "site_admin": false
            },
            "parents": []
        }
    ],
    "contributors": [
        {
            "login": "itz-rajkeshav",
            "id": 150330725,
            "node_id": "U_kgDOCPXdZQ",
            "avatar_url": "https://avatars.githubusercontent.com/u/150330725?v=4",
            "gravatar_id": "",
            "url": "https://api.github.com/users/itz-rajkeshav",
            "html_url": "https://github.com/itz-rajkeshav",
            "followers_url": "https://api.github.com/users/itz-rajkeshav/followers",
            "following_url": "https://api.github.com/users/itz-rajkeshav/following{/other_user}",
            "gists_url": "https://api.github.com/users/itz-rajkeshav/gists{/gist_id}",
            "starred_url": "https://api.github.com/users/itz-rajkeshav/starred{/owner}{/repo}",
            "subscriptions_url": "https://api.github.com/users/itz-rajkeshav/subscriptions",
            "organizations_url": "https://api.github.com/users/itz-rajkeshav/orgs",
            "repos_url": "https://api.github.com/users/itz-rajkeshav/repos",
            "events_url": "https://api.github.com/users/itz-rajkeshav/events{/privacy}",
            "received_events_url": "https://api.github.com/users/itz-rajkeshav/received_events",
            "type": "User",
            "user_view_type": "public",
            "site_admin": false,
            "contributions": 24
        }
    ],
    "content": [
        {
            "name": ".gitignore",
            "path": ".gitignore",
            "sha": "5ef6a520780202a1d6addd833d800ccb1ecac0bb",
            "size": 480,
            "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/contents/.gitignore?ref=main",
            "html_url": "https://github.com/itz-rajkeshav/Keshav.dev/blob/main/.gitignore",
            "git_url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/git/blobs/5ef6a520780202a1d6addd833d800ccb1ecac0bb",
            "download_url": "https://raw.githubusercontent.com/itz-rajkeshav/Keshav.dev/main/.gitignore?token=BD252ZP7EWNIFDNWEZRICFLITHBRE",
            "type": "file",
            "_links": {
                "self": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/contents/.gitignore?ref=main",
                "git": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/git/blobs/5ef6a520780202a1d6addd833d800ccb1ecac0bb",
                "html": "https://github.com/itz-rajkeshav/Keshav.dev/blob/main/.gitignore"
            }
        },
        {
            "name": "README.md",
            "path": "README.md",
            "sha": "e215bc4ccf138bbc38ad58ad57e92135484b3c0f",
            "size": 1450,
            "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/contents/README.md?ref=main",
            "html_url": "https://github.com/itz-rajkeshav/Keshav.dev/blob/main/README.md",
            "git_url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/git/blobs/e215bc4ccf138bbc38ad58ad57e92135484b3c0f",
            "download_url": "https://raw.githubusercontent.com/itz-rajkeshav/Keshav.dev/main/README.md?token=BD252ZLHDC7COUM7LWKWLB3ITHBRE",
            "type": "file",
            "_links": {
                "self": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/contents/README.md?ref=main",
                "git": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/git/blobs/e215bc4ccf138bbc38ad58ad57e92135484b3c0f",
                "html": "https://github.com/itz-rajkeshav/Keshav.dev/blob/main/README.md"
            }
        },
        {
            "name": "components.json",
            "path": "components.json",
            "sha": "ffe928f5b6dfe484f57a5fd47d0487f21e164fa3",
            "size": 430,
            "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/contents/components.json?ref=main",
            "html_url": "https://github.com/itz-rajkeshav/Keshav.dev/blob/main/components.json",
            "git_url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/git/blobs/ffe928f5b6dfe484f57a5fd47d0487f21e164fa3",
            "download_url": "https://raw.githubusercontent.com/itz-rajkeshav/Keshav.dev/main/components.json?token=BD252ZMHPYO65ZDFPNWNHPDITHBRE",
            "type": "file",
            "_links": {
                "self": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/contents/components.json?ref=main",
                "git": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/git/blobs/ffe928f5b6dfe484f57a5fd47d0487f21e164fa3",
                "html": "https://github.com/itz-rajkeshav/Keshav.dev/blob/main/components.json"
            }
        },
        {
            "name": "eslint.config.mjs",
            "path": "eslint.config.mjs",
            "sha": "c85fb67c463f20d1ee449b0ffee725a61dfb9259",
            "size": 393,
            "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/contents/eslint.config.mjs?ref=main",
            "html_url": "https://github.com/itz-rajkeshav/Keshav.dev/blob/main/eslint.config.mjs",
            "git_url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/git/blobs/c85fb67c463f20d1ee449b0ffee725a61dfb9259",
            "download_url": "https://raw.githubusercontent.com/itz-rajkeshav/Keshav.dev/main/eslint.config.mjs?token=BD252ZP7RI3IHNUH2XESYULITHBRE",
            "type": "file",
            "_links": {
                "self": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/contents/eslint.config.mjs?ref=main",
                "git": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/git/blobs/c85fb67c463f20d1ee449b0ffee725a61dfb9259",
                "html": "https://github.com/itz-rajkeshav/Keshav.dev/blob/main/eslint.config.mjs"
            }
        },
        {
            "name": "next.config.ts",
            "path": "next.config.ts",
            "sha": "943282351483b0830707376d3840794d035ec581",
            "size": 420,
            "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/contents/next.config.ts?ref=main",
            "html_url": "https://github.com/itz-rajkeshav/Keshav.dev/blob/main/next.config.ts",
            "git_url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/git/blobs/943282351483b0830707376d3840794d035ec581",
            "download_url": "https://raw.githubusercontent.com/itz-rajkeshav/Keshav.dev/main/next.config.ts?token=BD252ZJYGAQYVMQM3G2HZ2TITHBRE",
            "type": "file",
            "_links": {
                "self": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/contents/next.config.ts?ref=main",
                "git": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/git/blobs/943282351483b0830707376d3840794d035ec581",
                "html": "https://github.com/itz-rajkeshav/Keshav.dev/blob/main/next.config.ts"
            }
        },
        {
            "name": "package-lock.json",
            "path": "package-lock.json",
            "sha": "9bab88b1fb7d10224df22850989ff51fa22229b7",
            "size": 228011,
            "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/contents/package-lock.json?ref=main",
            "html_url": "https://github.com/itz-rajkeshav/Keshav.dev/blob/main/package-lock.json",
            "git_url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/git/blobs/9bab88b1fb7d10224df22850989ff51fa22229b7",
            "download_url": "https://raw.githubusercontent.com/itz-rajkeshav/Keshav.dev/main/package-lock.json?token=BD252ZOGME235R433ZPZB73ITHBRE",
            "type": "file",
            "_links": {
                "self": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/contents/package-lock.json?ref=main",
                "git": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/git/blobs/9bab88b1fb7d10224df22850989ff51fa22229b7",
                "html": "https://github.com/itz-rajkeshav/Keshav.dev/blob/main/package-lock.json"
            }
        },
        {
            "name": "package.json",
            "path": "package.json",
            "sha": "77aa6e3a2c1f51c8b858bf0c9e101affcf3f1ad8",
            "size": 1295,
            "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/contents/package.json?ref=main",
            "html_url": "https://github.com/itz-rajkeshav/Keshav.dev/blob/main/package.json",
            "git_url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/git/blobs/77aa6e3a2c1f51c8b858bf0c9e101affcf3f1ad8",
            "download_url": "https://raw.githubusercontent.com/itz-rajkeshav/Keshav.dev/main/package.json?token=BD252ZJZPPJNGRMWX2BO7TDITHBRE",
            "type": "file",
            "_links": {
                "self": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/contents/package.json?ref=main",
                "git": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/git/blobs/77aa6e3a2c1f51c8b858bf0c9e101affcf3f1ad8",
                "html": "https://github.com/itz-rajkeshav/Keshav.dev/blob/main/package.json"
            }
        },
        {
            "name": "postcss.config.mjs",
            "path": "postcss.config.mjs",
            "sha": "c7bcb4b1ee14cd5e25078c2c934529afdd2a7df9",
            "size": 81,
            "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/contents/postcss.config.mjs?ref=main",
            "html_url": "https://github.com/itz-rajkeshav/Keshav.dev/blob/main/postcss.config.mjs",
            "git_url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/git/blobs/c7bcb4b1ee14cd5e25078c2c934529afdd2a7df9",
            "download_url": "https://raw.githubusercontent.com/itz-rajkeshav/Keshav.dev/main/postcss.config.mjs?token=BD252ZKAG5TTCRQQFQVEH5DITHBRE",
            "type": "file",
            "_links": {
                "self": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/contents/postcss.config.mjs?ref=main",
                "git": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/git/blobs/c7bcb4b1ee14cd5e25078c2c934529afdd2a7df9",
                "html": "https://github.com/itz-rajkeshav/Keshav.dev/blob/main/postcss.config.mjs"
            }
        },
        {
            "name": "public",
            "path": "public",
            "sha": "71c4939a69e1e820da0b38893559f69f0e65d7c9",
            "size": 0,
            "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/contents/public?ref=main",
            "html_url": "https://github.com/itz-rajkeshav/Keshav.dev/tree/main/public",
            "git_url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/git/trees/71c4939a69e1e820da0b38893559f69f0e65d7c9",
            "download_url": null,
            "type": "dir",
            "_links": {
                "self": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/contents/public?ref=main",
                "git": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/git/trees/71c4939a69e1e820da0b38893559f69f0e65d7c9",
                "html": "https://github.com/itz-rajkeshav/Keshav.dev/tree/main/public"
            }
        },
        {
            "name": "src",
            "path": "src",
            "sha": "fdab5137d43c86a463272fca63a7a23b8cfde78a",
            "size": 0,
            "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/contents/src?ref=main",
            "html_url": "https://github.com/itz-rajkeshav/Keshav.dev/tree/main/src",
            "git_url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/git/trees/fdab5137d43c86a463272fca63a7a23b8cfde78a",
            "download_url": null,
            "type": "dir",
            "_links": {
                "self": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/contents/src?ref=main",
                "git": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/git/trees/fdab5137d43c86a463272fca63a7a23b8cfde78a",
                "html": "https://github.com/itz-rajkeshav/Keshav.dev/tree/main/src"
            }
        },
        {
            "name": "tailwind.config.js",
            "path": "tailwind.config.js",
            "sha": "1bb7440c0d6c07e050ce53a76c2e34853ad88974",
            "size": 345,
            "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/contents/tailwind.config.js?ref=main",
            "html_url": "https://github.com/itz-rajkeshav/Keshav.dev/blob/main/tailwind.config.js",
            "git_url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/git/blobs/1bb7440c0d6c07e050ce53a76c2e34853ad88974",
            "download_url": "https://raw.githubusercontent.com/itz-rajkeshav/Keshav.dev/main/tailwind.config.js?token=BD252ZI6DSTWCBDC3L73U4TITHBRE",
            "type": "file",
            "_links": {
                "self": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/contents/tailwind.config.js?ref=main",
                "git": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/git/blobs/1bb7440c0d6c07e050ce53a76c2e34853ad88974",
                "html": "https://github.com/itz-rajkeshav/Keshav.dev/blob/main/tailwind.config.js"
            }
        },
        {
            "name": "tsconfig.json",
            "path": "tsconfig.json",
            "sha": "c1334095f876a408c10f2357faaced969ec090ab",
            "size": 602,
            "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/contents/tsconfig.json?ref=main",
            "html_url": "https://github.com/itz-rajkeshav/Keshav.dev/blob/main/tsconfig.json",
            "git_url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/git/blobs/c1334095f876a408c10f2357faaced969ec090ab",
            "download_url": "https://raw.githubusercontent.com/itz-rajkeshav/Keshav.dev/main/tsconfig.json?token=BD252ZPUZVGTQKPMP3OWBY3ITHBRE",
            "type": "file",
            "_links": {
                "self": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/contents/tsconfig.json?ref=main",
                "git": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/git/blobs/c1334095f876a408c10f2357faaced969ec090ab",
                "html": "https://github.com/itz-rajkeshav/Keshav.dev/blob/main/tsconfig.json"
            }
        }
    ],
    "folderstructure": {
        "sha": "629c8ec22a935cf44ef73e0cafba9853d48eb511",
        "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/git/trees/629c8ec22a935cf44ef73e0cafba9853d48eb511",
        "tree": [
            {
                "path": ".gitignore",
                "mode": "100644",
                "type": "blob",
                "sha": "5ef6a520780202a1d6addd833d800ccb1ecac0bb",
                "size": 480,
                "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/git/blobs/5ef6a520780202a1d6addd833d800ccb1ecac0bb"
            },
            {
                "path": "README.md",
                "mode": "100644",
                "type": "blob",
                "sha": "e215bc4ccf138bbc38ad58ad57e92135484b3c0f",
                "size": 1450,
                "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/git/blobs/e215bc4ccf138bbc38ad58ad57e92135484b3c0f"
            },
            {
                "path": "components.json",
                "mode": "100644",
                "type": "blob",
                "sha": "ffe928f5b6dfe484f57a5fd47d0487f21e164fa3",
                "size": 430,
                "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/git/blobs/ffe928f5b6dfe484f57a5fd47d0487f21e164fa3"
            },
            {
                "path": "eslint.config.mjs",
                "mode": "100644",
                "type": "blob",
                "sha": "c85fb67c463f20d1ee449b0ffee725a61dfb9259",
                "size": 393,
                "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/git/blobs/c85fb67c463f20d1ee449b0ffee725a61dfb9259"
            },
            {
                "path": "next.config.ts",
                "mode": "100644",
                "type": "blob",
                "sha": "943282351483b0830707376d3840794d035ec581",
                "size": 420,
                "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/git/blobs/943282351483b0830707376d3840794d035ec581"
            },
            {
                "path": "package-lock.json",
                "mode": "100644",
                "type": "blob",
                "sha": "9bab88b1fb7d10224df22850989ff51fa22229b7",
                "size": 228011,
                "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/git/blobs/9bab88b1fb7d10224df22850989ff51fa22229b7"
            },
            {
                "path": "package.json",
                "mode": "100644",
                "type": "blob",
                "sha": "77aa6e3a2c1f51c8b858bf0c9e101affcf3f1ad8",
                "size": 1295,
                "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/git/blobs/77aa6e3a2c1f51c8b858bf0c9e101affcf3f1ad8"
            },
            {
                "path": "postcss.config.mjs",
                "mode": "100644",
                "type": "blob",
                "sha": "c7bcb4b1ee14cd5e25078c2c934529afdd2a7df9",
                "size": 81,
                "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/git/blobs/c7bcb4b1ee14cd5e25078c2c934529afdd2a7df9"
            },
            {
                "path": "public",
                "mode": "040000",
                "type": "tree",
                "sha": "71c4939a69e1e820da0b38893559f69f0e65d7c9",
                "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/git/trees/71c4939a69e1e820da0b38893559f69f0e65d7c9"
            },
            {
                "path": "public/asset",
                "mode": "040000",
                "type": "tree",
                "sha": "f9108b7d4884ee82b21cce1246f959101f643a27",
                "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/git/trees/f9108b7d4884ee82b21cce1246f959101f643a27"
            },
            {
                "path": "public/asset/ChatGPT Image May 2, 2025, 11_27_07 PM.png",
                "mode": "100644",
                "type": "blob",
                "sha": "83e5ae25b83e2e1f6f4b78f0f6e8dae4660df3d8",
                "size": 1128185,
                "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/git/blobs/83e5ae25b83e2e1f6f4b78f0f6e8dae4660df3d8"
            },
            {
                "path": "public/asset/Me.jpg",
                "mode": "100644",
                "type": "blob",
                "sha": "78c20e6ee47dbadb187e7d4a4201637f67d20f9c",
                "size": 121725,
                "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/git/blobs/78c20e6ee47dbadb187e7d4a4201637f67d20f9c"
            },
            {
                "path": "public/asset/camaflouge.png",
                "mode": "100644",
                "type": "blob",
                "sha": "5ec825409788533546ca66beda5ea50c10bc9cab",
                "size": 33783,
                "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/git/blobs/5ec825409788533546ca66beda5ea50c10bc9cab"
            },
            {
                "path": "public/asset/chatify.png",
                "mode": "100644",
                "type": "blob",
                "sha": "da227bfb129c160bb6d0f7c118d439f109d571ea",
                "size": 124866,
                "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/git/blobs/da227bfb129c160bb6d0f7c118d439f109d571ea"
            },
            {
                "path": "public/asset/keshav_new.pdf",
                "mode": "100644",
                "type": "blob",
                "sha": "732d40de6010ea6287c0e3a3ef8383a0b538224e",
                "size": 65336,
                "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/git/blobs/732d40de6010ea6287c0e3a3ef8383a0b538224e"
            },
            {
                "path": "public/asset/logo.png",
                "mode": "100644",
                "type": "blob",
                "sha": "71aca9b5294e2b24e6f8ba8120fb6141dffd2f72",
                "size": 96529,
                "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/git/blobs/71aca9b5294e2b24e6f8ba8120fb6141dffd2f72"
            },
            {
                "path": "public/asset/me1.png",
                "mode": "100644",
                "type": "blob",
                "sha": "a49f705e1a6578dd9d5781c25b758f4b21b6ae52",
                "size": 1600013,
                "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/git/blobs/a49f705e1a6578dd9d5781c25b758f4b21b6ae52"
            },
            {
                "path": "public/asset/tg_torrentBot.png",
                "mode": "100644",
                "type": "blob",
                "sha": "6b3107d676e940f794ede7c6d184737ec8d1ff2c",
                "size": 100259,
                "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/git/blobs/6b3107d676e940f794ede7c6d184737ec8d1ff2c"
            },
            {
                "path": "public/fonts",
                "mode": "040000",
                "type": "tree",
                "sha": "222435dafce55740e511aa0a3feffdce2d92f9d4",
                "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/git/trees/222435dafce55740e511aa0a3feffdce2d92f9d4"
            },
            {
                "path": "public/fonts/Raleway-VariableFont_wght.ttf",
                "mode": "100644",
                "type": "blob",
                "sha": "33969e8558009027421748910fb952d447cc4650",
                "size": 311856,
                "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/git/blobs/33969e8558009027421748910fb952d447cc4650"
            },
            {
                "path": "public/fonts/WinkyRough-VariableFont_wght.ttf",
                "mode": "100644",
                "type": "blob",
                "sha": "daccb657b75339d362886c6147e15ebe5a2246c3",
                "size": 345420,
                "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/git/blobs/daccb657b75339d362886c6147e15ebe5a2246c3"
            },
            {
                "path": "public/fonts/YujiMai-Regular.ttf",
                "mode": "100644",
                "type": "blob",
                "sha": "a02a58c5f1116e281957847611bef05826dda779",
                "size": 8438824,
                "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/git/blobs/a02a58c5f1116e281957847611bef05826dda779"
            },
            {
                "path": "src",
                "mode": "040000",
                "type": "tree",
                "sha": "fdab5137d43c86a463272fca63a7a23b8cfde78a",
                "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/git/trees/fdab5137d43c86a463272fca63a7a23b8cfde78a"
            },
            {
                "path": "src/app",
                "mode": "040000",
                "type": "tree",
                "sha": "cab4f5a7a580acbd2994e7b2e80d43df1b732369",
                "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/git/trees/cab4f5a7a580acbd2994e7b2e80d43df1b732369"
            },
            {
                "path": "src/app/component",
                "mode": "040000",
                "type": "tree",
                "sha": "c9ea17310884b31e10777df7517dfb37bb08bcd9",
                "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/git/trees/c9ea17310884b31e10777df7517dfb37bb08bcd9"
            },
            {
                "path": "src/app/component/AnonymousWall.tsx",
                "mode": "100644",
                "type": "blob",
                "sha": "92dcbe766d64460c7d82dc12f03e736819be3d1f",
                "size": 40240,
                "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/git/blobs/92dcbe766d64460c7d82dc12f03e736819be3d1f"
            },
            {
                "path": "src/app/component/Contact.tsx",
                "mode": "100644",
                "type": "blob",
                "sha": "9e0585262d3b93ca5ceac81549e1b764f2d1e368",
                "size": 6970,
                "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/git/blobs/9e0585262d3b93ca5ceac81549e1b764f2d1e368"
            },
            {
                "path": "src/app/component/FirstPage.tsx",
                "mode": "100644",
                "type": "blob",
                "sha": "fd8bb8eca6fa352bcdf48a4da394513009e57804",
                "size": 17895,
                "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/git/blobs/fd8bb8eca6fa352bcdf48a4da394513009e57804"
            },
            {
                "path": "src/app/component/Footer.tsx",
                "mode": "100644",
                "type": "blob",
                "sha": "7d5d03bd47c39a9980f91aa954232091f9b61839",
                "size": 6888,
                "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/git/blobs/7d5d03bd47c39a9980f91aa954232091f9b61839"
            },
            {
                "path": "src/app/component/SecondPage.tsx",
                "mode": "100644",
                "type": "blob",
                "sha": "7ca7feef49566bc4a7dd9805c0956028a58d761d",
                "size": 14417,
                "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/git/blobs/7ca7feef49566bc4a7dd9805c0956028a58d761d"
            },
            {
                "path": "src/app/component/SkillAndJourney.tsx",
                "mode": "100644",
                "type": "blob",
                "sha": "4649cfb40a5742f3c014353e1b2d94a2b9afc349",
                "size": 19720,
                "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/git/blobs/4649cfb40a5742f3c014353e1b2d94a2b9afc349"
            },
            {
                "path": "src/app/component/ThirdPage.tsx",
                "mode": "100644",
                "type": "blob",
                "sha": "ab6dddbf98c5e61e632daa1da20bc35813642893",
                "size": 9540,
                "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/git/blobs/ab6dddbf98c5e61e632daa1da20bc35813642893"
            },
            {
                "path": "src/app/component/navbar.tsx",
                "mode": "100644",
                "type": "blob",
                "sha": "2ce9fb55bc43b3572ff4473fc88d8df2813f6c5a",
                "size": 12234,
                "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/git/blobs/2ce9fb55bc43b3572ff4473fc88d8df2813f6c5a"
            },
            {
                "path": "src/app/globals.css",
                "mode": "100644",
                "type": "blob",
                "sha": "69fd3e33eb42632da28706e892d22a952124327e",
                "size": 6644,
                "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/git/blobs/69fd3e33eb42632da28706e892d22a952124327e"
            },
            {
                "path": "src/app/layout.tsx",
                "mode": "100644",
                "type": "blob",
                "sha": "f9e12cb9d778d1233df9c41f09b9fe18376052cd",
                "size": 961,
                "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/git/blobs/f9e12cb9d778d1233df9c41f09b9fe18376052cd"
            },
            {
                "path": "src/app/page.tsx",
                "mode": "100644",
                "type": "blob",
                "sha": "403d8b6d0b1428fcde77c02426fd6094d979b41c",
                "size": 823,
                "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/git/blobs/403d8b6d0b1428fcde77c02426fd6094d979b41c"
            },
            {
                "path": "src/app/ui",
                "mode": "040000",
                "type": "tree",
                "sha": "a04e401f497f64fc13f6e48442912247174c9de2",
                "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/git/trees/a04e401f497f64fc13f6e48442912247174c9de2"
            },
            {
                "path": "src/app/ui/AnimatedButton.tsx",
                "mode": "100644",
                "type": "blob",
                "sha": "44cd33dae79d4f9c9953f3621b0fa154f94d499e",
                "size": 2012,
                "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/git/blobs/44cd33dae79d4f9c9953f3621b0fa154f94d499e"
            },
            {
                "path": "src/app/ui/GradientText.tsx",
                "mode": "100644",
                "type": "blob",
                "sha": "17955d5c1af3f29c09ad047a79c88cf22cf0225b",
                "size": 2114,
                "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/git/blobs/17955d5c1af3f29c09ad047a79c88cf22cf0225b"
            },
            {
                "path": "src/app/ui/SplashCursor.tsx",
                "mode": "100644",
                "type": "blob",
                "sha": "7da67755d9cfed319d8a9ad27501ed633ddc603c",
                "size": 49089,
                "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/git/blobs/7da67755d9cfed319d8a9ad27501ed633ddc603c"
            },
            {
                "path": "src/app/ui/SpotlightCard.tsx",
                "mode": "100644",
                "type": "blob",
                "sha": "993a31bfefb61e1767548d4568b08b7edb5d73f1",
                "size": 2978,
                "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/git/blobs/993a31bfefb61e1767548d4568b08b7edb5d73f1"
            },
            {
                "path": "src/app/ui/github.tsx",
                "mode": "100644",
                "type": "blob",
                "sha": "0727fd15969168baa624f80bacd7de467a893295",
                "size": 4651,
                "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/git/blobs/0727fd15969168baa624f80bacd7de467a893295"
            },
            {
                "path": "src/app/ui/instagram.tsx",
                "mode": "100644",
                "type": "blob",
                "sha": "55622487d7753e1cfe860af0b3965d4a49c6a179",
                "size": 5417,
                "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/git/blobs/55622487d7753e1cfe860af0b3965d4a49c6a179"
            },
            {
                "path": "src/app/ui/linkedin.tsx",
                "mode": "100644",
                "type": "blob",
                "sha": "191894f2caeaf6152ea3793d22de007bd8eacc43",
                "size": 5400,
                "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/git/blobs/191894f2caeaf6152ea3793d22de007bd8eacc43"
            },
            {
                "path": "src/app/ui/mail-check.tsx",
                "mode": "100644",
                "type": "blob",
                "sha": "84467d0e1e4966918c9678ff1e7ec796acbf3288",
                "size": 2884,
                "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/git/blobs/84467d0e1e4966918c9678ff1e7ec796acbf3288"
            },
            {
                "path": "src/lib",
                "mode": "040000",
                "type": "tree",
                "sha": "905e7c8ba8e376922d84e6e232cac7272075d842",
                "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/git/trees/905e7c8ba8e376922d84e6e232cac7272075d842"
            },
            {
                "path": "src/lib/utils.ts",
                "mode": "100644",
                "type": "blob",
                "sha": "56440d1c4db7a2c9cbf6d61713fd58c7e946da1d",
                "size": 326,
                "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/git/blobs/56440d1c4db7a2c9cbf6d61713fd58c7e946da1d"
            },
            {
                "path": "tailwind.config.js",
                "mode": "100644",
                "type": "blob",
                "sha": "1bb7440c0d6c07e050ce53a76c2e34853ad88974",
                "size": 345,
                "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/git/blobs/1bb7440c0d6c07e050ce53a76c2e34853ad88974"
            },
            {
                "path": "tsconfig.json",
                "mode": "100644",
                "type": "blob",
                "sha": "c1334095f876a408c10f2357faaced969ec090ab",
                "size": 602,
                "url": "https://api.github.com/repos/itz-rajkeshav/Keshav.dev/git/blobs/c1334095f876a408c10f2357faaced969ec090ab"
            }
        ],
        "truncated": false
    },
    "stars": 0,
    "forks": 0,
    "url": "https://github.com/itz-rajkeshav/Keshav.dev",
    "created_at": "2025-04-23T17:09:19Z",
    "updated_at": "2025-07-06T11:15:45Z"
}
    dispatch(setCommit(data.commit));
    if (status === "authenticated") {
      router.push(`/dashboard`);
    } else {
      signIn();
    }
  };

  return (
    <div className="bg-gray-900 min-h-screen w-full text-white font-['Comic_Neue'] relative overflow-hidden">
      <div className="absolute inset-0 bg-gradient-to-br from-purple-900/20 via-black to-cyan-900/20"></div>
      <div className="absolute top-0 left-1/4 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl"></div>
      <div className="absolute bottom-0 right-1/4 w-96 h-96 bg-cyan-500/10 rounded-full blur-3xl"></div>

      <div className="relative z-10">
        <header className="flex justify-between items-center px-8 py-6">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-cyan-500 rounded-full flex items-center justify-center shadow-lg shadow-purple-500/25">
              <div className="w-4 h-4 bg-white rounded-full"></div>
            </div>
            <span className="text-2xl font-bold bg-gradient-to-r from-purple-400 to-cyan-400 bg-clip-text text-transparent">
              GitGist
            </span>
          </div>

          <div className="flex items-center space-x-4">
            {status === "loading" ? (
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-purple-400"></div>
            ) : session ? (
              <div className="w-14 h-14 bg-gradient-to-r from-pink-600 to-pink-800 rounded-full flex items-center justify-center mx-auto  shadow-lg shadow-pink-500/25 group-hover:scale-110 transition-transform duration-300">
                <span className="text-gray-300 text-xl font-semibold">
                  {(session.user?.name || session.user?.email)
                    ?.trim()
                    .charAt(0)}
                </span>
              </div>
            ) : (
              <button
                onClick={() => signIn()}
                className="px-8 py-4 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors duration-200"
              >
                Sign in
              </button>
            )}
          </div>
        </header>

        <main className="flex flex-col items-center justify-center px-8 py-16 max-w-4xl mx-auto text-center">
          <h1 className="text-5xl md:text-7xl font-bold mb-8 leading-tight text-white">
            Simplify GitHub
            <br />
            <span className="text-blue-400">Repository Exploration</span>
          </h1>

          <p className="text-xl text-gray-300 mb-12 max-w-2xl leading-relaxed">
            Get the best details about any repo, just from its link.
          </p>

          <div className="flex flex-col sm:flex-row gap-4 w-full max-w-2xl mb-16">
            <input
              type="text"
              placeholder="Enter a GitHub repository URL"
              value={repoUrl}
              onChange={(e) => setRepoUrl(e.target.value)}
              onKeyDown={(e) => e.key === "Enter" && handleExplore()}
              className="flex-1 px-6 py-4 bg-gray-900/50 backdrop-blur-sm border border-purple-500/30 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-400 transition-all duration-300 hover:border-purple-400/50"
            />
            <button
              onClick={handleExplore}
              className="px-8 py-4 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors duration-200"
            >
              Explore
            </button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-8 mb-16 w-full max-w-4xl">
            <div className="text-center group">
              <div className="w-16 h-16 bg-gradient-to-r from-purple-600 to-purple-800 rounded-full flex items-center justify-center mx-auto mb-4 shadow-lg shadow-purple-500/25 group-hover:scale-110 transition-transform duration-300">
                <span className="text-white font-bold text-xl">1</span>
              </div>
              <h3 className="text-lg font-bold mb-2 text-purple-200">
                Easy to use
              </h3>
            </div>

            <div className="text-center group">
              <div className="w-16 h-16 bg-gradient-to-r from-cyan-600 to-cyan-800 rounded-full flex items-center justify-center mx-auto mb-4 shadow-lg shadow-cyan-500/25 group-hover:scale-110 transition-transform duration-300">
                <span className="text-white font-bold text-xl">2</span>
              </div>
              <h3 className="text-lg font-bold mb-2 text-cyan-200">
                In-depth summaries
              </h3>
            </div>

            <div className="text-center group">
              <div className="w-16 h-16 bg-gradient-to-r from-pink-600 to-pink-800 rounded-full flex items-center justify-center mx-auto mb-4 shadow-lg shadow-pink-500/25 group-hover:scale-110 transition-transform duration-300">
                <span className="text-white font-bold text-xl">3</span>
              </div>
              <h3 className="text-lg font-bold mb-2 text-pink-200">
                Quick insights
              </h3>
            </div>

            <div className="text-center group">
              <div className="w-16 h-16 bg-gradient-to-r from-emerald-600 to-emerald-800 rounded-full flex items-center justify-center mx-auto mb-4 shadow-lg shadow-emerald-500/25 group-hover:scale-110 transition-transform duration-300">
                <span className="text-white font-bold text-xl">4</span>
              </div>
              <h3 className="text-lg font-bold mb-2 text-emerald-200">
                Q&A regarding the repository
              </h3>
            </div>
          </div>

          <div className="max-w-2xl">
            <p className="text-xl text-gray-300 leading-relaxed font-medium">
              New developers often struggle to quickly understand what a GitHub
              repository contains and its significance.
            </p>
          </div>
        </main>
      </div>
    </div>
  );
};

export default Heropage;
