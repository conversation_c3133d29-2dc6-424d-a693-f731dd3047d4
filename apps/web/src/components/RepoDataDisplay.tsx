"use client";

import React from "react";
import { useSelector } from "react-redux";
import { RootState } from "../../../../packages/store/store";

const RepoDataDisplay = () => {
  // Access data from Redux store
  const { repoData, commit, folderstructure } = useSelector((state: RootState) => state.repo);

  if (repoData.length === 0) {
    return (
      <div className="p-6 bg-slate-800 rounded-lg">
        <p className="text-gray-400">No repository data available. Please analyze a repository first.</p>
      </div>
    );
  }

  const repo = repoData[0]; // Get the first repository data

  return (
    <div className="space-y-6">
      {/* Repository Basic Info */}
      <div className="p-6 bg-slate-800 rounded-lg">
        <h2 className="text-2xl font-bold text-white mb-4">Repository Information</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <h3 className="text-lg font-semibold text-purple-400">Name</h3>
            <p className="text-white">{repo.name}</p>
          </div>
          <div>
            <h3 className="text-lg font-semibold text-purple-400">Description</h3>
            <p className="text-white">{repo.description || "No description"}</p>
          </div>
          <div>
            <h3 className="text-lg font-semibold text-purple-400">Stars</h3>
            <p className="text-white">{repo.stars}</p>
          </div>
          <div>
            <h3 className="text-lg font-semibold text-purple-400">Forks</h3>
            <p className="text-white">{repo.forks}</p>
          </div>
          <div>
            <h3 className="text-lg font-semibold text-purple-400">Created</h3>
            <p className="text-white">{new Date(repo.created_at).toLocaleDateString()}</p>
          </div>
          <div>
            <h3 className="text-lg font-semibold text-purple-400">Updated</h3>
            <p className="text-white">{new Date(repo.updated_at).toLocaleDateString()}</p>
          </div>
        </div>
      </div>

      {/* Languages */}
      <div className="p-6 bg-slate-800 rounded-lg">
        <h2 className="text-2xl font-bold text-white mb-4">Languages</h2>
        <div className="space-y-2">
          {Object.entries(repo.language).map(([lang, bytes]) => (
            <div key={lang} className="flex justify-between items-center">
              <span className="text-cyan-400">{lang}</span>
              <span className="text-white">{bytes.toLocaleString()} bytes</span>
            </div>
          ))}
        </div>
      </div>

      {/* Recent Commits */}
      <div className="p-6 bg-slate-800 rounded-lg">
        <h2 className="text-2xl font-bold text-white mb-4">Recent Commits ({commit.length})</h2>
        <div className="space-y-3 max-h-96 overflow-y-auto">
          {commit.slice(0, 10).map((commitItem, index) => (
            <div key={index} className="border-l-4 border-purple-500 pl-4 py-2">
              <p className="text-white font-medium">{commitItem.commit.message}</p>
              <div className="text-sm text-gray-400 mt-1">
                <span>By {commitItem.commit.author.name}</span>
                <span className="ml-4">{new Date(commitItem.commit.author.date).toLocaleDateString()}</span>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Folder Structure */}
      <div className="p-6 bg-slate-800 rounded-lg">
        <h2 className="text-2xl font-bold text-white mb-4">Folder Structure ({folderstructure.length} items)</h2>
        <div className="space-y-2 max-h-96 overflow-y-auto">
          {folderstructure.slice(0, 20).map((item, index) => (
            <div key={index} className="flex items-center space-x-3 py-1">
              <span className={`text-sm px-2 py-1 rounded ${
                item.type === 'tree' ? 'bg-blue-600' : 
                item.type === 'blob' ? 'bg-green-600' : 'bg-gray-600'
              }`}>
                {item.type}
              </span>
              <span className="text-white font-mono text-sm">{item.path}</span>
              {item.size > 0 && (
                <span className="text-gray-400 text-xs ml-auto">{item.size} bytes</span>
              )}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default RepoDataDisplay;
