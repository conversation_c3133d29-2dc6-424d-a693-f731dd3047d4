// GitHub API Types
export interface GitHubCommitAuthor {
    name: string;
    email: string;
    date: string;
}

export interface GitHubCommitTree {
    sha: string;
    url: string;
}

export interface GitHubCommitData {
    author: GitHubCommitAuthor;
    committer: GitHubCommitAuthor;
    message: string;
    tree: GitHubCommitTree;
    url: string;
    comment_count: number;
    verification?: {
        verified: boolean;
        reason: string;
        signature: string | null;
        payload: string | null;
    };
}

export interface GitHubCommit {
    sha: string;
    node_id: string;
    commit: GitHubCommitData;
    url: string;
    html_url: string;
    comments_url: string;
    author: {
        login: string;
        id: number;
        node_id: string;
        avatar_url: string;
        gravatar_id: string;
        url: string;
        html_url: string;
        followers_url: string;
        following_url: string;
        gists_url: string;
        starred_url: string;
        subscriptions_url: string;
        organizations_url: string;
        repos_url: string;
        events_url: string;
        received_events_url: string;
        type: string;
        site_admin: boolean;
    } | null;
    committer: {
        login: string;
        id: number;
        node_id: string;
        avatar_url: string;
        gravatar_id: string;
        url: string;
        html_url: string;
        followers_url: string;
        following_url: string;
        gists_url: string;
        starred_url: string;
        subscriptions_url: string;
        organizations_url: string;
        repos_url: string;
        events_url: string;
        received_events_url: string;
        type: string;
        site_admin: boolean;
    } | null;
    parents: Array<{
        sha: string;
        url: string;
        html_url: string;
    }>;
}

export interface GitHubContributor {
    login: string;
    id: number;
    node_id: string;
    avatar_url: string;
    gravatar_id: string;
    url: string;
    html_url: string;
    followers_url: string;
    following_url: string;
    gists_url: string;
    starred_url: string;
    subscriptions_url: string;
    organizations_url: string;
    repos_url: string;
    events_url: string;
    received_events_url: string;
    type: string;
    site_admin: boolean;
    contributions: number;
}

export interface GitHubFileContent {
    name: string;
    path: string;
    sha: string;
    size: number;
    url: string;
    html_url: string;
    git_url: string;
    download_url: string | null;
    type: 'file' | 'dir';
    content?: string;
    encoding?: string;
    _links: {
        self: string;
        git: string;
        html: string;
    };
}

export interface GitHubFolderStructure {
    sha: string;
    url: string;
    tree: Array<{
        path: string;
        mode: string;
        type: 'blob' | 'tree' | 'commit';
        size?: number;
        sha: string;
        url: string;
    }>;
    truncated: boolean;
}

// Complete Repository Data Interface
export interface CompleteRepoData {
    name: string;
    description: string | null;
    language: Record<string, number>;
    commits: GitHubCommit[];
    contributors: GitHubContributor[];
    content: GitHubFileContent[];
    folderstructure: GitHubFolderStructure;
    stars: number;
    forks: number;
    url: string;
    created_at: string;
    updated_at: string;
}

// Legacy interfaces for backward compatibility
export interface githubFile {
    folderstructure: {
        path: string;
        mode: string;
        type: 'blob' | 'tree' | 'commit';
        size: number;
        sha: string;
        url: string;
    };
}

export interface RepoData {
    name: string;
    description: string;
    language: Record<string, number>;
    stars: number;
    forks: number;
    url: string;
    updated_at: string;
    created_at: string;
}

export interface commit {
    commit: {
        author: {
            name: string;
            email: string;
            date: string;
        };
        message: string;
        tree: {
            sha: string;
            url: string;
        };
        comment_count: number;
    };
}